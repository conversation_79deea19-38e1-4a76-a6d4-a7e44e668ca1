import i18n from '@/constants/lang';
import SCREENID from '@/constants/screenId';
import { InfoShowType } from '@/types/InfoShowTypes';
import { CustomFormType } from '@/types/CustomFormTypes';
import { rules } from '@/utils/validator';
import { createFormModelByFormItems } from '@/utils/customForm';

const { t } = i18n.global;

export const getProcessDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 品名
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Sjg.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // ロットアウト品
    lotoutFlg: {
      label: { text: t('Sjg.Chr.txtVerityReason') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 使用期限
    expiryYmd: {
      label: { text: t('Sjg.Chr.txtExpiryYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 出来高日
    rsltYmd: {
      label: { text: t('Sjg.Chr.txtRsltYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
  });

export const getConfirmDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 品名
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Sjg.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // 照査結果
    verifyNm: {
      label: { text: t('Sjg.Chr.txtVerifyOdrRslt') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 照査者
    verifyUsrNm: {
      label: { text: t('Sjg.Chr.txtVerifyUsrNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // コメント
    verifyExpl: {
      label: { text: t('Sjg.Chr.txtVerifyExplComment') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
  });

// 照査完了確認コメントのアイテム定義
export const getInspectionCompletionFormItems = (
  screenId: string,
): CustomFormType['formItems'] => {
  const formItems: CustomFormType['formItems'] = {
    odrRslt: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Sjg.Chr.txtCarRslt') },
      formModelValue: '',
      rules: [rules.required('selectComboBox')],
      formRole: 'selectComboBox',
      props: {
        optionsData: [],
        disabled:
          screenId ===
          SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION_CONFIRMATION,
      },
      selectOptions: [],
      cmbId: 'cmbOdrRslt',
    },
    rsltVerifyExpl: {
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      label: { text: t('Inv.Chr.txtComment') },
      formModelValue: '',
      rules: [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ],
      formRole: 'textComboBox',
      props: {
        clearable: true,
        disabled:
          screenId ===
          SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION_CONFIRMATION,
        size: 'custom',
        width: '100%',
      },
      selectOptions: [],
      cmbId: 'cmtSjgVeriQuality',
    },
  };
  if (
    screenId ===
    SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION_CONFIRMATION
  ) {
    formItems.odrRslt.tags = [];
    formItems.odrRslt.rules = [];
    formItems.rsltVerifyExpl.tags = [];
    formItems.rsltVerifyExpl.rules = [];
  }
  return formItems;
};

// ロック解除ダイアログのモデル定義
export const inspectionCompletionFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getInspectionCompletionFormItems(''));
