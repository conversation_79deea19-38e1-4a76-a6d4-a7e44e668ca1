import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

export const getProcessDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Sjg.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 品名
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 24,
    },
    // ロットアウト品
    verifyReasonNm: {
      label: { text: t('Sjg.Chr.txtVerityReason') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 使用期限
    expiryYmd: {
      label: { text: t('Sjg.Chr.txtExpiryYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
    // 出来高日
    rsltYmd: {
      label: { text: t('Sjg.Chr.txtRsltYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 8,
    },
  });

export const tablePropsDataVerifyResultList: TabulatorTableIF = {
  pageName: 'VerifyResultList',
  pageSize: 20,
  height: '280',
  dataID: 'uniqueKey',
  hideCheckboxTitleFormatter: true,
  showCheckbox: {
    show: true,
    condition: 'checkBoxDisabled',
    conditionValue: 0,
    allAllowed: false,
  },
  selectRowsData: [],
  rowHeight: 35,
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 50, // ボタン列幅
    condition: 'isEnableBtn',
    conditionValue: 0,
    disableText: 'disable1',
    btnProps: { text: t('Cm.Chr.btnDetail'), size: 'small', type: 'secondary' },
  },
  column: [
    {
      title: 'Sjg.Chr.txtOdrNo',
      field: 'odrNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    {
      title: 'Sjg.Chr.txtMatNo',
      field: 'matNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    { title: 'Sjg.Chr.txtMatNm', field: 'matNm', width: COLUMN_WIDTHS.MAT_NM },
    {
      title: 'Sjg.Chr.txtBomModList',
      field: 'bomModListExistNm',
      width: COLUMN_WIDTHS.SJG.MOD_LIST,
    },
    {
      title: 'Sjg.Chr.txtPrdModList',
      field: 'prdModListExistNm',
      width: COLUMN_WIDTHS.SJG.MOD_LIST,
    },
    {
      title: 'Sjg.Chr.txtSopModList',
      field: 'sopModListExistNm',
      width: COLUMN_WIDTHS.SJG.MOD_LIST,
    },
    {
      title: 'Sjg.Chr.txtDevList',
      field: 'devLogListExistNm',
      width: COLUMN_WIDTHS.SJG.DEV_LIST,
    },
    {
      title: 'Sjg.Chr.txtOrderRecordApprovalDates',
      field: 'recApprovDts',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtOrderRecordApprovalUser',
      field: 'recApprovUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtOrderRecordApprovalExpl',
      field: 'recApprovExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
    {
      title: '',
      field: 'isEnableBtn',
      formatter: 'btn',
      width: 170,
      hozAlign: 'center',
    },

    // チェックボックス活性・非活性判定の隠しカラム
    {
      title: '',
      field: 'checkBoxDisabled',
      hidden: true,
    },
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
};
