<template>
  <!-- 指図登録ダイアログ -->
  <DialogWindow
    :title="$t('Odr.Chr.txtOrderRegistration')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onReject="commonRejectHandler"
    :onResolve="resolveClickHandler"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <CustomForm
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="dialogFormRef.formModel"
      :formItems="dialogFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          dialogFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- 製造番号発番ボタン押下チェック表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxPressedLotNoIssuanceVisible"
    :dialogProps="messageBoxPressedLotNoIssuancePropsRef"
    :submitCallback="() => closeDialog('messageBoxPressedLotNoIssuanceVisible')"
  />
  <!-- 製造番号情報取得前チェックのワーニング表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxCheckBeforeGetLotNoInfoWarningVisible"
    :dialogProps="messageBoxCheckBeforeGetLotNoInfoWarningPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxCheckBeforeGetLotNoInfoWarningVisible')
    "
    :submitCallback="
      () => {
        closeDialog('messageBoxCheckBeforeGetLotNoInfoWarningVisible');
        // チェックOKなら処理継続する。
        // 製造番号情報取得API呼び出し
        requestApiGetLotNoInfo();
      }
    "
  />
  <!-- 指図登録前チェックのワーニング表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxCheckBeforeAddOrderWarningVisible"
    :dialogProps="messageBoxCheckBeforeAddOrderWarningPropsRef"
    :cancelCallback="
      () => closeDialog('messageBoxCheckBeforeAddOrderWarningVisible')
    "
    :submitCallback="
      () => {
        closeDialog('messageBoxCheckBeforeAddOrderWarningVisible');
        // チェックOKなら処理継続する。
        // 指図登録API呼び出し
        requestApiAddOrder();
      }
    "
  />
  <!-- 製造指図登録の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxRegistOrderVisible"
    :dialogProps="messageBoxRegistOrderPropsRef"
    :cancelCallback="() => closeDialog('messageBoxRegistOrderVisible')"
    :submitCallback="requestApiCheckBeforeAddOrder"
  />
  <!-- 製造指図登録の完了表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxRegistOrderFinishedVisible"
    :dialogProps="messageBoxRegistOrderFinishedPropsRef"
    :submitCallback="closeDialogFromMessageBoxAddOrder"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch, nextTick } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import CONST from '@/constants/utils';
import CustomForm from '@/components/parts/CustomForm.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import { CustomFormType, FormItem } from '@/types/CustomFormTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import {
  GetApprovalScheduleListData,
  GetOrderAddInitRequestData,
  GetOrderAddInitResData,
  CheckBeforeGetLotNoInfoRequestData,
  GetLotNoInfoRequestData,
  CheckBeforeAddOrderRequestData,
  AddOrderRequestData,
} from '@/types/HookUseApi/OdrTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import { getFilesForApiRequest, getRemovedFileKeys } from '@/utils/fileUpload';
import { rules } from '@/utils/validator';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  useGetOrderAddInit,
  useCheckBeforeGetLotNoInfo,
  useGetLotNoInfo,
  useCheckBeforeAddOrder,
  useAddOrder,
} from '@/hooks/useApi';
import {
  FORM_NAME,
  FILE_ODR_ATT_BIN,
  OrderAppendixDataType,
  AddAttBinData,
  getDialogFormItems,
  dialogFormModel,
} from './odrRegistOrder';

/**
 * 多言語
 */
const { t } = useI18n();

// CustomFormのfileUploadを動作させる為の中間保持データ
let orderAppendixData: OrderAppendixDataType = {
  [FORM_NAME.ODR_ATT_BIN_LIST]: [
    {
      binFileNm: '',
      appBinNo: '',
    },
  ],
  skdNo: '', // オーダー番号
  planPrcNo: '', // 計画番号
  matNo: '', // 品目コード
  dspNmJp: '', // 品名
  rxNmJp: '', // 処方名
  mbrNo: '', // MBR番号
  validYmd: '', // 処方有効期限
  stdPrdQty: '', // 標準生産量
  planQty: '', // 計画生産量
  unitNmJp: '', // 単位
  odrDts: '', // 製造開始予定日
  expiryDtsType: '', // 使用期限区分
  expiryDtsFlg: false, // 使用期限日活性フラグ
  expiryCalcDtsType: '', // 使用期限起算日区分
  expiryCalcDtsFlg: false, // 使用期限起算日活性フラグ
  shelfLifeDtsType: '', // 有効期限区分
  shelfLifeDtsFlg: false, // 有効期限日活性フラグ
  shelfLifeCalcDtsType: '', // 有効期限起算日区分
  shelfLifeCalcDtsFlg: false, // 有効期限起算日活性フラグ
  handOverTxt: '', // 指図コメント
  binFileNm: '', // 特別作業指示ファイル名
  appBinNo: '', // 特別作業指示管理No
  skdUpdDts: '', // 小日程計画更新日時
  odrUpdDts: '', // 製造指図更新日時
};

// NOTE:ファイル1つだけしか添付できないため、単体で定義
// 追加用ファイルデータ
let cacheAddBinData: AddAttBinData = {
  [FILE_ODR_ATT_BIN.FILE_KEY]: '',
  [FILE_ODR_ATT_BIN.NAME_KEY]: '',
};
// NOTE:ファイル1つだけしか添付できないため、単体で定義
// 削除用ファイルNo
let cacheDelBinNo: string = '';

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxPressedLotNoIssuanceVisible'
  | 'messageBoxCheckBeforeGetLotNoInfoWarningVisible'
  | 'messageBoxCheckBeforeAddOrderWarningVisible'
  | 'messageBoxRegistOrderVisible'
  | 'messageBoxRegistOrderFinishedVisible';

// ダイアログの表示切替用定義
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxPressedLotNoIssuanceVisible: false,
  messageBoxCheckBeforeGetLotNoInfoWarningVisible: false,
  messageBoxCheckBeforeAddOrderWarningVisible: false,
  messageBoxRegistOrderVisible: false,
  messageBoxRegistOrderFinishedVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const dialogFormRef = ref<CustomFormType>({
  formItems: getDialogFormItems(),
  formModel: dialogFormModel,
});

// カスタムフォームの描画更新トリガー
const customFormRenderingTriggerRef = ref(false);

// 指図登録初期表示のレスポンスデータ
let initResponseData: GetOrderAddInitResData = {
  skdNo: '', // オーダー番号
  planPrcNo: '', // 計画番号
  matNo: '', // 品目コード
  dspNmJp: '', // 品名
  rxNmJp: '', // 処方名
  mbrNo: '', // MBR番号
  validYmd: '', // 処方有効期限
  stdPrdQty: '', // 標準生産量
  planQty: '', // 計画生産量
  unitNmJp: '', // 単位
  odrDts: '', // 製造開始予定日
  expiryDtsType: '', // 使用期限区分
  expiryDtsFlg: false, // 使用期限日活性フラグ
  expiryCalcDtsType: '', // 使用期限起算日区分
  expiryCalcDtsFlg: false, // 使用期限起算日活性フラグ
  shelfLifeDtsType: '', // 有効期限区分
  shelfLifeDtsFlg: false, // 有効期限日活性フラグ
  shelfLifeCalcDtsType: '', // 有効期限起算日区分
  shelfLifeCalcDtsFlg: false, // 有効期限起算日活性フラグ
  handOverTxt: '', // 指図コメント
  binFileNm: '', // 特別作業指示ファイル名
  appBinNo: '', // 特別作業指示管理No
  skdUpdDts: '', // 小日程計画更新日時
  odrUpdDts: '', // 製造指図更新日時
};

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// 製造番号発番ボタン押下チェック表示のメッセージボックス
const messageBoxPressedLotNoIssuancePropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleLotNoIssuance'),
  content: t('Odr.Msg.contentLotNoIssuance'),
  isSingleBtn: true,
  type: 'error',
});

// 製造番号情報取得前チェック ワーニングのメッセージボックス
const messageBoxCheckBeforeGetLotNoInfoWarningPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  type: 'warning',
});

// 指図登録前チェック ワーニングのメッセージボックス
const messageBoxCheckBeforeAddOrderWarningPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  type: 'warning',
});

// 指図登録の確認メッセージボックス
const messageBoxRegistOrderPropsRef = ref<DialogProps>({
  title: t('Odr.Msg.titleRegistOrder'),
  content: t('Odr.Msg.contentRegistOrder'),
  type: 'question',
});

// 指図登録の完了メッセージボックス
const messageBoxRegistOrderFinishedPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'info',
});

// 共通APIリクエストに渡すための、直前MessageBox表示テキストの保存情報
type MessageBoxInfo = {
  titleText: string;
  messageText: string;
  buttonText: string;
};

let messageBoxInfo: MessageBoxInfo = {
  titleText: '',
  messageText: '',
  buttonText: '',
};

// NOTE:前チェックAPIでワーニングの有無によって直前メッセージが変わるため、それを保存するための機構
// 直前MessageBox表示テキストの保存
const cacheMessageBoxInfo = (v: DialogProps) => {
  messageBoxInfo = {
    titleText: v.title,
    messageText: v.content,
    buttonText: t('Cm.Chr.btnOk'),
  };
};

// 直前MessageBox表示テキストの初期化
const clearMessageBoxInfo = () => {
  messageBoxInfo = {
    titleText: '',
    messageText: '',
    buttonText: '',
  };
};

type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  selectedRowData: GetApprovalScheduleListData | null; // 親ページの選択行情報
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);
let isPressedButtonLotNoIssuance = false; // 製造番号発番ボタンが押下されたか
// 自身の 実行 押下時処理
const resolveClickHandler = async () => {
  // 1.下記チェックを行い、チェックOKなら処理継続する。
  // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
  const validate =
    dialogFormRef.value.customForm !== undefined &&
    (await dialogFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return false;
  }

  if (!isPressedButtonLotNoIssuance) {
    // 2 製造番号発番ボタン押下チェック表示
    openDialog('messageBoxPressedLotNoIssuanceVisible');
    return false;
  }

  let addOdrAttBinList: AddAttBinData[] = [];
  let delOdrAttBinNoList: string[] = [];
  // NOTE: アップロードファイル数は最大1で設定されている（maxLength: 1）

  // [fileUpload共通仕様] APIリクエストで渡すファイル情報を取得
  // NOTE: addOdrAttBinListには新規追加のアップロードファイルのみセットされる
  addOdrAttBinList = await getFilesForApiRequest<AddAttBinData>(
    dialogFormRef.value.formModel[FILE_ODR_ATT_BIN.MODEL_KEY],
    {
      fileNameKey: FILE_ODR_ATT_BIN.NAME_KEY,
      fileKey: FILE_ODR_ATT_BIN.FILE_KEY,
    },
  );

  delOdrAttBinNoList = getRemovedFileKeys(
    dialogFormRef.value.formModel[FILE_ODR_ATT_BIN.MODEL_KEY],
    {
      initialFileList: orderAppendixData[FORM_NAME.ODR_ATT_BIN_LIST],
      fileKeyPropName: FILE_ODR_ATT_BIN.UNIQUE_KEY,
    },
  );

  // NOTE:アップロードファイル数は最大1のため、先頭だけ取得してキャッシュ
  if (addOdrAttBinList[0] !== undefined) {
    [cacheAddBinData] = addOdrAttBinList;
  } else {
    cacheAddBinData = {
      [FILE_ODR_ATT_BIN.FILE_KEY]: '',
      [FILE_ODR_ATT_BIN.NAME_KEY]: '',
    };
  }
  if (delOdrAttBinNoList[0] !== undefined) {
    [cacheDelBinNo] = delOdrAttBinNoList;
  } else {
    cacheDelBinNo = '';
  }

  // NOTE:指図登録は既にアップロードしたファイルを選択していても問題ない仕様。
  //      その場合は追加、削除が空のままリクエストパラメータに設定される。

  // 指図登録の確認メッセージボックス表示
  openDialog('messageBoxRegistOrderVisible');

  return false;
};

// NOTE:元々はnumber型だったが、decimal-string対応で戻り値をstringに変更
// 製造指図量 取得
const getOrderQuantity = () =>
  dialogFormRef.value.formItems.odrQty.formModelValue.toString();

// 指図登録のAPIリクエスト処理
const requestApiAddOrder = async () => {
  showLoading();

  // ４．製造指図を登録する。
  const requestData: AddOrderRequestData = {
    skdNo: initResponseData.skdNo, // オーダー番号
    lotNo:
      dialogFormRef.value.formItems[
        FORM_NAME.DSP_LOT_NO
      ].formModelValue.toString(), // 製造番号
    odrStDts:
      dialogFormRef.value.formItems[
        FORM_NAME.ODR_DTS
      ].formModelValue.toString(), // 製造開始予定日
    odrQty: getOrderQuantity(), // 製造指図量
    shelfLifeCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_CALC_DTS
      ].formModelValue.toString(), // 有効期限起算日
    shelfLifeDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_DTS
      ].formModelValue.toString(), // 有効期限日
    expiryCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_CALC_DTS
      ].formModelValue.toString(), // 使用期限起算日
    expiryDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_DTS
      ].formModelValue.toString(), // 使用期限日
    handOverTxt:
      dialogFormRef.value.formItems[
        FORM_NAME.HAND_OVER_TXT
      ].formModelValue.toString(), // 指図コメント
    addAttBinFileNm: cacheAddBinData[FILE_ODR_ATT_BIN.NAME_KEY], // 追加用特別作業指示ファイル名
    addAttBinFile: cacheAddBinData[FILE_ODR_ATT_BIN.FILE_KEY], // 追加用特別作業指示ファイルオブジェクト
    delAttBinNo: cacheDelBinNo, // 削除用特別作業指示ファイル管理No
    skdUpdDts: initResponseData.skdUpdDts, // 小日程計画更新日時
    odrUpdDts: initResponseData.odrUpdDts, // 製造指図更新日時
  };
  const { responseRef, errorRef } = await useAddOrder({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxInfo.titleText,
    msgboxMsgTxt: messageBoxInfo.messageText,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();

    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  closeLoading();

  // ・データベースを更新した後、以下のメッセージを表示する。
  // 「製造指図を登録しました。」
  messageBoxRegistOrderFinishedPropsRef.value.title =
    responseRef.value.data.rTitle;
  messageBoxRegistOrderFinishedPropsRef.value.content =
    responseRef.value.data.rMsg;

  openDialog('messageBoxRegistOrderFinishedVisible');
};

// 指図登録の確認メッセージ'OK'押下時処理
// 指図登録前チェックのAPIリクエスト処理
const requestApiCheckBeforeAddOrder = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxRegistOrderVisible');

  // 確認メッセージ情報を保存
  cacheMessageBoxInfo(messageBoxRegistOrderPropsRef.value);

  showLoading();

  // 以下のチェックはBEで行う。FEとしてはAPIをリクエストする。
  // 3.下記チェックを行い、チェックOKなら処理継続する。
  const requestData: CheckBeforeAddOrderRequestData = {
    skdNo: initResponseData.skdNo, // オーダー番号
    lotNo:
      dialogFormRef.value.formItems[
        FORM_NAME.DSP_LOT_NO
      ].formModelValue.toString(), // 製造番号
    odrStDts:
      dialogFormRef.value.formItems[
        FORM_NAME.ODR_DTS
      ].formModelValue.toString(), // 製造開始予定日
    odrQty: getOrderQuantity(), // 製造指図量
    shelfLifeCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_CALC_DTS
      ].formModelValue.toString(), // 有効期限起算日
    shelfLifeDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_DTS
      ].formModelValue.toString(), // 有効期限日
    expiryCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_CALC_DTS
      ].formModelValue.toString(), // 使用期限起算日
    expiryDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_DTS
      ].formModelValue.toString(), // 使用期限日
    handOverTxt:
      dialogFormRef.value.formItems[
        FORM_NAME.HAND_OVER_TXT
      ].formModelValue.toString(), // 指図コメント
    addAttBinFileNm: cacheAddBinData[FILE_ODR_ATT_BIN.NAME_KEY], // 追加用特別作業指示ファイル名
    addAttBinFile: cacheAddBinData[FILE_ODR_ATT_BIN.FILE_KEY], // 追加用特別作業指示ファイルオブジェクト
    delAttBinNo: cacheDelBinNo, // 削除用特別作業指示ファイル管理No
    skdUpdDts: initResponseData.skdUpdDts, // 小日程計画更新日時
    odrUpdDts: initResponseData.odrUpdDts, // 製造指図更新日時
  };

  const { responseRef, errorRef } = await useCheckBeforeAddOrder({
    ...props.privilegesBtnRequestData,
    msgboxTitleTxt: messageBoxRegistOrderPropsRef.value.title,
    msgboxMsgTxt: messageBoxRegistOrderPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      // 指図登録前チェック ワーニング用メッセージボックス起動
      messageBoxCheckBeforeAddOrderWarningPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxCheckBeforeAddOrderWarningPropsRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('messageBoxCheckBeforeAddOrderWarningVisible');

      // ワーニングメッセージを保存
      cacheMessageBoxInfo(messageBoxCheckBeforeAddOrderWarningPropsRef.value);

      return; // ワーニングの場合、ワーニング用messageBox側で処理継続させる。
    }
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return; // レスポンスがない場合継続処理させない
  }

  closeLoading();

  // 指図登録のAPI呼び出し
  requestApiAddOrder();
};

// 指図登録に紐づくダイアログを閉じる処理
const closeDialogFromMessageBoxAddOrder = () => {
  // 再検索用に親に実行を通知
  emit('submit', props.privilegesBtnRequestData);

  // 自身を閉じる
  closeDialog('messageBoxRegistOrderFinishedVisible');
  // 5.ダイアログウィンドウを閉じる
  closeDialog('fragmentDialogVisible');
};

// フォーム名、種類に応じたRules取得
// @param {string} formName - フォーム名
// @param {string} formRole - フォームの種類
const getRules = (formName: string, formRole: string) => {
  let overrideRules: FormItem['rules'] = [];

  // NOTE:工数削減のため、必要なformRoleのみ対応。
  switch (formRole) {
    case 'date':
      // NOTE:一律で設定するが、個別に設定する場合は影響範囲を良く見て修正をお願いします。
      // 必須、過去日禁止
      overrideRules = [rules.required('date'), rules.futureDate()];
      break;
    case 'textBox':
      overrideRules = [rules.required('textBox')];
      // キー毎の特殊ルール追加
      if (formName === FORM_NAME.DSP_LOT_NO) {
        // NOTE:製造番号はFEは15桁でチェック。実際は条件で桁が変わるが、それはBEでチェックする。
        overrideRules.push(rules.length(15));
        // NOTE:製造番号は半角英数記号チェックも行う。
        overrideRules.push(
          rules.upperCaseSingleByteAlphanumericNumberCharacters(),
        );
      }
      break;
    default:
      // ここに来たなら追加実装をお願いします。
      console.error('formRole is not found.', formRole, formName);
      break;
  }
  return overrideRules;
};

// 指定カスタムフォームのタグ、ルール設定
// @param {string} formName - 設定先フォーム名
// @param {boolean} isRequired - 必須にするか。true:必須 false:任意
const setTagAndRulesRequiredCustomForm = (
  formName: string,
  isRequired: boolean,
) => {
  if (
    !(
      dialogFormRef.value.formModel && formName in dialogFormRef.value.formModel
    )
  ) {
    console.error('formName is not found in formModel.', formName);
    return;
  }

  let overrideTags: FormItem['tags'] = [];
  let overrideRules: FormItem['rules'] = [];

  if (isRequired) {
    overrideTags = [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }];

    overrideRules = getRules(
      formName,
      dialogFormRef.value.formItems[formName].formRole,
    );
  } else {
    overrideTags = [];
    overrideRules = [];
  }

  dialogFormRef.value.formItems[formName].tags = overrideTags;
  dialogFormRef.value.formItems[formName].rules = overrideRules;
};

// 指定カスタムフォームの非活性状態設定
// @param {string} formName - 設定先フォーム名
// @param {boolean} isDisabled - diabledの値。true:非活性 false:活性化
const setDisabledCustomForm = (formName: string, isDisabled: boolean) => {
  if (
    !(
      dialogFormRef.value.formModel && formName in dialogFormRef.value.formModel
    )
  ) {
    console.error('formName is not found in formModel.', formName);
    return;
  }

  // NOTE:工数削減のため、必要なformRoleのみ対応。
  switch (dialogFormRef.value.formItems[formName].formRole) {
    case 'date':
      dialogFormRef.value.formItems[formName].props.disabled = isDisabled;
      break;
    case 'textBox':
      // NOTE:textBoxの場合、設定方法が特殊なためpropsを強制上書きする。
      dialogFormRef.value.formItems[formName].props = {
        disabled: isDisabled,
      };
      break;
    default:
      // ここに来たなら追加実装をお願いします。
      console.error('formRole is not found.', formName);
      break;
  }
};

// 製造番号 見出し設定
// @param {string} type - 製造番号発番区分のテキスト
const setLabelDspLotNo = (type: string) => {
  // NOTE:'製造番号(製造番号発番区分:{type})' という形式になる
  dialogFormRef.value.formItems[FORM_NAME.DSP_LOT_NO].label = {
    text: t('Odr.Chr.txtLotNoAndIssuanceType', [type]),
  };
};

// 製造番号情報取得のAPIリクエスト処理
const requestApiGetLotNoInfo = async () => {
  // 製造番号情報取得API呼び出し
  const requestData: GetLotNoInfoRequestData = {
    skdNo: initResponseData.skdNo, // オーダー番号
    odrStDts:
      dialogFormRef.value.formItems[
        FORM_NAME.ODR_DTS
      ].formModelValue.toString(), // 製造開始予定日
    expiryDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_DTS
      ].formModelValue.toString(), // 使用期限日
    expiryCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_CALC_DTS
      ].formModelValue.toString(), // 使用期限起算日
    shelfLifeDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_DTS
      ].formModelValue.toString(), // 有効期限日
    shelfLifeCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_CALC_DTS
      ].formModelValue.toString(), // 有効期限起算日
  };

  showLoading();
  const { responseRef, errorRef } = await useGetLotNoInfo({
    ...props.privilegesBtnRequestData,
    ...requestData,
    msgboxTitleTxt: messageBoxInfo.titleText,
    msgboxMsgTxt: messageBoxInfo.messageText,
    msgboxBtnTxt: messageBoxInfo.buttonText,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // レスポンスと一致したフォームに対して値設定
  setFormModelValueFromApiResponse(dialogFormRef, responseRef.value.data.rData);

  // 使用期限日、使用期限起算日、有効期限日、有効期限起算日を非活性化させる
  const disabled = true;
  setDisabledCustomForm(FORM_NAME.EXPIRY_DTS, disabled);
  setDisabledCustomForm(FORM_NAME.EXPIRY_CALC_DTS, disabled);
  setDisabledCustomForm(FORM_NAME.SHELF_LIFE_DTS, disabled);
  setDisabledCustomForm(FORM_NAME.SHELF_LIFE_CALC_DTS, disabled);
  // 追加仕様対応：製造開始予定日を非活性化させる
  setDisabledCustomForm(FORM_NAME.ODR_DTS, disabled);

  // 使用期限日、使用期限起算日、有効期限日、有効期限起算日の必須タグを外す
  const isRequired = false;
  setTagAndRulesRequiredCustomForm(FORM_NAME.EXPIRY_DTS, isRequired);
  setTagAndRulesRequiredCustomForm(FORM_NAME.EXPIRY_CALC_DTS, isRequired);
  setTagAndRulesRequiredCustomForm(FORM_NAME.SHELF_LIFE_DTS, isRequired);
  setTagAndRulesRequiredCustomForm(FORM_NAME.SHELF_LIFE_CALC_DTS, isRequired);
  // 追加仕様対応：製造開始予定日の必須タグを外す
  setTagAndRulesRequiredCustomForm(FORM_NAME.ODR_DTS, isRequired);

  // ・製造番号発番区分が手入力の場合、その入力項目を活性化させる
  const lotNoDisabled = !responseRef.value.data.rData.lotNoFlg;
  setDisabledCustomForm(FORM_NAME.DSP_LOT_NO, lotNoDisabled);

  // 製造番号 見出し設定
  setLabelDspLotNo(responseRef.value.data.rData.lotNoType);
  // NOTE:labelとtagsを同時編集限定の特殊対応。label変更後にDOMが再生成されるまで待つ必要がある。
  await nextTick();
  // 必須状態、必須タグの設定
  setTagAndRulesRequiredCustomForm(FORM_NAME.DSP_LOT_NO, !lotNoDisabled);

  // カスタムフォームに変更があったことを通知。活性非活性切り替え用。
  customFormRenderingTriggerRef.value = !customFormRenderingTriggerRef.value;

  // ボタン押下されたことを記憶
  isPressedButtonLotNoIssuance = true;

  closeLoading();
};

// 製造番号情報取得前チェックのAPIリクエスト処理
const requestApiCheckBeforeGetLotNoInfo = async () => {
  // 製造番号情報取得前チェックAPI呼び出し
  const requestData: CheckBeforeGetLotNoInfoRequestData = {
    skdNo: initResponseData.skdNo, // オーダー番号
    odrStDts:
      dialogFormRef.value.formItems[
        FORM_NAME.ODR_DTS
      ].formModelValue.toString(), // 製造開始予定日
    expiryDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_DTS
      ].formModelValue.toString(), // 使用期限日
    expiryCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.EXPIRY_CALC_DTS
      ].formModelValue.toString(), // 使用期限起算日
    shelfLifeDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_DTS
      ].formModelValue.toString(), // 有効期限日
    shelfLifeCalcDts:
      dialogFormRef.value.formItems[
        FORM_NAME.SHELF_LIFE_CALC_DTS
      ].formModelValue.toString(), // 有効期限起算日
  };

  showLoading();
  const { responseRef, errorRef } = await useCheckBeforeGetLotNoInfo({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      // 製造番号情報取得前チェック ワーニング用メッセージボックス起動
      messageBoxCheckBeforeGetLotNoInfoWarningPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxCheckBeforeGetLotNoInfoWarningPropsRef.value.content =
        errorRef.value.response.rMsg;
      openDialog('messageBoxCheckBeforeGetLotNoInfoWarningVisible');

      // ワーニングメッセージを保存
      cacheMessageBoxInfo(
        messageBoxCheckBeforeGetLotNoInfoWarningPropsRef.value,
      );

      return; // ワーニングの場合、ワーニング用messageBox側で処理継続させる。
    }
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  closeLoading();

  // ここまで来たら正常フロー
  // 製造番号情報取得API呼び出し
  await requestApiGetLotNoInfo();
};

// ButtonFormのクリック時イベントの設定
const resetButtonFormOnClickHandler = () => {
  // NOTE:一度変数に入れないとなぜか型ガードが動作しなくなるためキャッシュ
  const key = FORM_NAME.BUTTON_SCHEDULED_DATE;
  // '製造番号発番'ボタンイベント
  if (dialogFormRef.value.formItems[key].formRole !== 'button') return;

  dialogFormRef.value.formItems[key].onClickHandler = async () => {
    // 1.下記チェックを行い、チェックOKなら処理継続する。
    // １ 各コントロールのチェック内容をチェックする（NG時：動作は共通仕様参照）
    const validate =
      dialogFormRef.value.customForm !== undefined &&
      (await dialogFormRef.value.customForm.validate((isValid) => {
        onValidateHandler(isValid);
      }));

    if (!validate) {
      return;
    }

    // NOTE:製造番号情報取得はメッセージ無しか、ワーニングメッセージの可能性がある
    // 直前MessageBox情報の初期化
    clearMessageBoxInfo();

    // 製造番号情報取得前チェックAPI呼び出し
    await requestApiCheckBeforeGetLotNoInfo();
  };
};

// 使用期限日 見出し設定
// @param {string} type - 使用期限区分のテキスト
const setLabelExpiryDts = (type: string) => {
  // NOTE:'使用期限日(使用期限区分:{type})' という形式になる
  dialogFormRef.value.formItems[FORM_NAME.EXPIRY_DTS].label = {
    text: t('Odr.Chr.txtExpiryDateAndType', [type]),
  };
};

// 使用期限起算日 見出し設定
// @param {string} type - 使用期限起算日区分のテキスト
const setLabelExpiryCalcDts = (type: string) => {
  // NOTE:'使用期限起算日(使用期限起算日区分:{type})' という形式になる
  dialogFormRef.value.formItems[FORM_NAME.EXPIRY_CALC_DTS].label = {
    text: t('Odr.Chr.txtUseExpiryDateAndCalculationType', [type]),
  };
};

// 有効期限日 見出し設定
// @param {string} type - 有効期限区分のテキスト
const setLabelShelfLifeDts = (type: string) => {
  // NOTE:'有効期限日(有効期限区分:{type})' という形式になる
  dialogFormRef.value.formItems[FORM_NAME.SHELF_LIFE_DTS].label = {
    text: t('Odr.Chr.txtEffectiveDateAndType', [type]),
  };
};

// 使用期限起算日 見出し設定
// @param {string} type - 有効期限起算日区分のテキスト
const setLabelShelfLifeCalcDts = (type: string) => {
  // NOTE:'有効期限起算日(有効期限起算日区分:{type})' という形式になる
  dialogFormRef.value.formItems[FORM_NAME.SHELF_LIFE_CALC_DTS].label = {
    text: t('Odr.Chr.txtEffectiveCalcDateAndCalculationType', [type]),
  };
};

// fileUpload用中間保持データの初期化
const clearOrderAppendixData = () => {
  orderAppendixData = {
    [FORM_NAME.ODR_ATT_BIN_LIST]: [
      {
        binFileNm: '',
        appBinNo: '',
      },
    ],
    skdNo: '', // オーダー番号
    planPrcNo: '', // 計画番号
    matNo: '', // 品目コード
    dspNmJp: '', // 品名
    rxNmJp: '', // 処方名
    mbrNo: '', // MBR番号
    validYmd: '', // 処方有効期限
    stdPrdQty: '', // 標準生産量
    planQty: '', // 計画生産量
    unitNmJp: '', // 単位
    odrDts: '', // 製造開始予定日
    expiryDtsType: '', // 使用期限区分
    expiryDtsFlg: false, // 使用期限日活性フラグ
    expiryCalcDtsType: '', // 使用期限起算日区分
    expiryCalcDtsFlg: false, // 使用期限起算日活性フラグ
    shelfLifeDtsType: '', // 有効期限区分
    shelfLifeDtsFlg: false, // 有効期限日活性フラグ
    shelfLifeCalcDtsType: '', // 有効期限起算日区分
    shelfLifeCalcDtsFlg: false, // 有効期限起算日活性フラグ
    handOverTxt: '', // 指図コメント
    binFileNm: '', // 特別作業指示ファイル名
    appBinNo: '', // 特別作業指示管理No
    skdUpdDts: '', // 小日程計画更新日時
    odrUpdDts: '', // 製造指図更新日時
  };
};
/**
 * 指図登録ダイアログの初期設定
 */
const odrRegistOrderInit = async () => {
  // NOTE:未選択なら処理させない。エラーメッセージはTabulatorTable側で出してくれる。
  if (props.selectedRowData === null) return;

  isPressedButtonLotNoIssuance = false;
  updateDialogChangeFlagRef(false);
  // FormItems初期化
  dialogFormRef.value.formItems = getDialogFormItems();

  clearOrderAppendixData();

  // ButtonFormのクリック時イベントの設定
  resetButtonFormOnClickHandler();

  // 指図登録初期表示API呼び出し
  const requestData: GetOrderAddInitRequestData = {
    skdNo: props.selectedRowData.skdNo, // オーダー番号
  };

  showLoading();
  const { responseRef, errorRef } = await useGetOrderAddInit({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });
  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value === undefined) {
    closeLoading();
    return;
  }

  // 指図登録初期表示のレスポンスデータを保持
  initResponseData = responseRef.value.data.rData;

  // ファイル名、特別作業指示管理Noがある場合のみ、CustomFormのfileUpload形式に準拠した表示用Refを作成
  if (initResponseData.binFileNm !== '' && initResponseData.appBinNo !== '') {
    // CustomFormのfileUploadを動作させる為の中間保持データ
    // NOTE: fileUpload共通仕様に合わせる為、レスポンスデータレイアウトを変更
    orderAppendixData = {
      [FORM_NAME.ODR_ATT_BIN_LIST]: [
        {
          binFileNm: initResponseData.binFileNm.toString(),
          appBinNo: initResponseData.appBinNo.toString(),
        },
      ],
      ...initResponseData, // NOTE:fileUpload以外のCustomFormの初期値をレスポンスパラメータで設定
    };

    // CustomFormのfileUpload形式に準拠した表示用Refを共通関数で作成
    setFormModelValueFromApiResponse(
      dialogFormRef, // 出力：CustomFormのRef
      orderAppendixData, // 入力：Apiレスポンスの中間保持データ
      {
        // NOTE:
        //   fileModel.fileKeys: ファイル情報：ファイル添付（初期値）が必要なときに設定する
        //   formModelKey: FormItems[key].formRole === 'fileUpload'のときのkey,
        //   fileNameKey: APIで指定されているファイル名キー,
        //   fileKeyPropName: APIで指定されているファイル管理Noキー
        fileKeys: [
          {
            formModelKey: FILE_ODR_ATT_BIN.MODEL_KEY,
            fileNameKey: FILE_ODR_ATT_BIN.NAME_KEY,
            fileKeyPropName: FILE_ODR_ATT_BIN.UNIQUE_KEY,
          },
        ],
        // ファイルダウンロード時に渡すAPI共通リクエストパラメータ
        commonRequestData: props.privilegesBtnRequestData,
      },
    );
  } else {
    // NOTE:fileUpload以外のCustomFormの初期値をレスポンスパラメータで設定
    // 指図登録レイアウト用初期値設定
    setFormModelValueFromApiResponse(dialogFormRef, initResponseData);
  }

  // NOTE:製造指図量初期値設定 ※小日程計画の計画生産量を初期値とする
  dialogFormRef.value.formItems.odrQty.formModelValue =
    initResponseData.planQty?.toString() ?? '';

  // カスタムフォーム見出しの設定
  setLabelDspLotNo(''); // NOTE:製造番号は区分の初期値は空白となる
  setLabelExpiryDts(initResponseData.expiryDtsType);
  setLabelExpiryCalcDts(initResponseData.expiryCalcDtsType);
  setLabelShelfLifeDts(initResponseData.shelfLifeDtsType);
  setLabelShelfLifeCalcDts(initResponseData.shelfLifeCalcDtsType);

  // 活性状態の設定
  setDisabledCustomForm(FORM_NAME.EXPIRY_DTS, !initResponseData.expiryDtsFlg);
  setDisabledCustomForm(
    FORM_NAME.EXPIRY_CALC_DTS,
    !initResponseData.expiryCalcDtsFlg,
  );
  setDisabledCustomForm(
    FORM_NAME.SHELF_LIFE_DTS,
    !initResponseData.shelfLifeDtsFlg,
  );
  setDisabledCustomForm(
    FORM_NAME.SHELF_LIFE_CALC_DTS,
    !initResponseData.shelfLifeCalcDtsFlg,
  );

  // 必須状態、必須タグの設定
  setTagAndRulesRequiredCustomForm(
    FORM_NAME.EXPIRY_DTS,
    initResponseData.expiryDtsFlg,
  );
  setTagAndRulesRequiredCustomForm(
    FORM_NAME.EXPIRY_CALC_DTS,
    initResponseData.expiryCalcDtsFlg,
  );
  setTagAndRulesRequiredCustomForm(
    FORM_NAME.SHELF_LIFE_DTS,
    initResponseData.shelfLifeDtsFlg,
  );
  setTagAndRulesRequiredCustomForm(
    FORM_NAME.SHELF_LIFE_CALC_DTS,
    initResponseData.shelfLifeCalcDtsFlg,
  );

  closeLoading();

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    odrRegistOrderInit();
  },
);
</script>
