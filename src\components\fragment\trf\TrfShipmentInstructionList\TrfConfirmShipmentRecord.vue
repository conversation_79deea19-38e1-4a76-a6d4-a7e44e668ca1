<template>
  <!-- 出庫実績確定 -->
  <DialogWindow
    :title="$t('Trf.Chr.txtInstructionConfirm')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :onReject="commonRejectHandler"
    :onResolve="async () => confirmMessageHandler()"
    @visible="updateDialogChangeFlagRef"
  >
    <p>{{ $t('Trf.Chr.txtConfirmRecordDialogTitle') }}</p>
    <div class="custom-form-container">
      <CustomForm
        :formModel="trfConfirmShipmentInstructionRecordFormRef.formModel"
        :formItems="trfConfirmShipmentInstructionRecordFormRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            trfConfirmShipmentInstructionRecordFormRef.customForm = v;
          }
        "
      />
    </div>
    <div class="Util_mt-32">
      <BaseHeading
        level="2"
        fontSize="24px"
        :text="$t('Trf.Chr.txtInstructionDetailsList')"
      />
      <TabulatorTable :propsData="tablePropsData" />
    </div>
    <BaseHeading
      level="2"
      fontSize="24px"
      :text="$t('Trf.Chr.txtConfirmRecordComment')"
    />
    <div class="custom-form-container">
      <CustomForm
        :formModel="explFormRef.formModel"
        :formItems="explFormRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            explFormRef.customForm = v;
          }
        "
        @changeFormModel="updateDialogChangeFlagRef"
      />
    </div>
    <!-- 出庫実績確定実施確認 -->
    <MessageBox
      v-if="dialogVisibleRef.trfConfirmShipmentRecordConfirm"
      :dialogProps="messageBoxConfirmProps"
      :cancelCallback="() => closeDialog('trfConfirmShipmentRecordConfirm')"
      :submitCallback="
        () => {
          apiHandler(messageBoxConfirmProps);
        }
      "
    />
  </DialogWindow>
  <!-- 異常 -->
  <MessageBox
    v-if="dialogVisibleRef.errorConfirm"
    :dialogProps="messageBoxErrorPropsRef"
    :submitCallback="() => closeDialog('errorConfirm')"
  />
  <!-- 情報 -->
  <MessageBox
    v-if="dialogVisibleRef.infoConfirm"
    :dialogProps="messageBoxInfoPropsRef"
    :submitCallback="closeAllDialog"
  />
  <!-- 強制完了実施確認のワーニングメッセージ -->
  <MessageBox
    v-if="dialogVisibleRef.trfForceCompleteConfirmBeforeAddWarning"
    :dialogProps="messageBoxForceCompleteConfirmPropsRef"
    :cancelCallback="
      () => closeDialog('trfForceCompleteConfirmBeforeAddWarning')
    "
    :submitCallback="
      () => {
        closeDialog('trfForceCompleteConfirmBeforeAddWarning');
        apiHandler(
          messageBoxForceCompleteConfirmPropsRef,
          WARNING_FLAG_FORCE_COMP.UNNECESSARY,
        );
      }
    "
  />
</template>
<script setup lang="ts">
import { ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import CONST from '@/constants/utils';
import { isNullOrEmpty } from '@/utils/index';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  TrfInstListData,
  TrfInstDetailResultData,
  AddTransferShipmentInstructionResultReq,
} from '@/types/HookUseApi/TrfTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { DialogProps } from '@/types/MessageBoxTypes';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import {
  useGetTransferShipmentInstructionDetailResult,
  useAddTransferShipmentInstructionResult,
  useGetComboBoxDataStandard,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  trfConfirmShipmentInstructionRecordFormModel,
  getTrfConfirmShipmentInstructionRecordFormItems,
  explFormModel,
  getExplFormItems,
  tablePropsData,
} from './trfConfirmShipmentRecord';

/**
 * 多言語
 */
const { t } = useI18n();

const trfConfirmShipmentInstructionRecordFormRef = ref<CustomFormType>({
  formItems: getTrfConfirmShipmentInstructionRecordFormItems(),
  formModel: trfConfirmShipmentInstructionRecordFormModel,
});

const explFormRef = ref<CustomFormType>({
  formItems: getExplFormItems(),
  formModel: explFormModel,
});

type Props = {
  selectedRows: TrfInstListData[];
  isClicked: boolean;
  privilegesBtnRequestData: CommonRequestType;
};
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'trfConfirmShipmentRecordConfirm'
  | 'trfForceCompleteConfirmBeforeAddWarning'
  | 'errorConfirm'
  | 'infoConfirm';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  trfConfirmShipmentRecordConfirm: false,
  trfForceCompleteConfirmBeforeAddWarning: false,
  errorConfirm: false,
  infoConfirm: false,
};
const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

/**
 *
 * 警告要否フラグ
 * - UNNECESSARY: 警告が不要であることを表します（値: 0）。
 * - NECESSITY: 警告が必要であることを表します（値: 1）：デフォルト値。
 */
const WARNING_FLAG_FORCE_COMP = {
  UNNECESSARY: 0,
  NECESSITY: 1,
} as const;

let comboBoxResData: ComboBoxDataStandardReturnData | undefined;

let trfInstDetailResultFormData: TrfInstDetailResultData = {
  trfInstGrpNo: '',
  prYmd: '',
  instUsr: '',
  prtDts: '',
  planNo: '',
  planUsr: '',
  srcZoneNm: '',
  instStsNm: '',
  dstZoneNm: '',
  matNo: '',
  matNm: '',
  lotNo: '',
  edNo: '',
  planQty: '',
  instQty: '',
  rsltQty: '',
  unitNm: '',
  fnpckHtExpl: '',
  trfInstGrpUpdDts: '',
  trfInstUpdDts: '',
};

const messageBoxConfirmProps: DialogProps = {
  title: t('Trf.Chr.txtTrfConfirmShipmentInstructionRecordConfirm'),
  content: t('Trf.Msg.trfConfirmShipmentInstructionRecordConfirm'),
  type: 'question',
};
const messageBoxErrorPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});
const messageBoxInfoPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxForm = createMessageBoxForm('message', 'trfRsltForceExpl');
const messageBoxForceCompleteConfirmPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

const confirmMessageHandler = async () => {
  openDialog('trfConfirmShipmentRecordConfirm');
  return false;
};
const apiHandler = async (
  messageBoxProps: DialogProps,
  warnFlgForceComp: number = WARNING_FLAG_FORCE_COMP.NECESSITY,
) => {
  closeDialog('trfConfirmShipmentRecordConfirm');
  showLoading();
  let trfConfirmShipmentInstructionRecordReq: ExtendCommonRequestType<AddTransferShipmentInstructionResultReq> =
    {
      ...props.privilegesBtnRequestData,
      trfInstGrpNo:
        trfConfirmShipmentInstructionRecordFormRef.value.formModel.trfInstGrpNo.toString(),
      dsrYmd: isNullOrEmpty(
        trfConfirmShipmentInstructionRecordFormRef.value.formModel.prYmd,
      )
        ? ''
        : trfConfirmShipmentInstructionRecordFormRef.value.formModel.prYmd.toString(),
      dstZoneNo:
        trfConfirmShipmentInstructionRecordFormRef.value.formModel.dstZoneNm.toString(),
      trfPlanNo:
        trfConfirmShipmentInstructionRecordFormRef.value.formModel.planNo.toString(),
      expl: explFormRef.value.formModel.expl.toString(),
      forceExpl: '',
      trfInstGrpUpdDts: trfInstDetailResultFormData.trfInstGrpUpdDts,
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      warnFlgForceComp,
    };

  if (
    'isPrompt' in messageBoxProps &&
    warnFlgForceComp === WARNING_FLAG_FORCE_COMP.UNNECESSARY &&
    comboBoxResData
  ) {
    trfConfirmShipmentInstructionRecordReq = {
      ...trfConfirmShipmentInstructionRecordReq,
      msgboxInputCmt: messageBoxProps.formModel.message.toString(),
      forceExpl: messageBoxProps.formModel.message.toString(),
    };
  }

  const { responseRef, errorRef } =
    await useAddTransferShipmentInstructionResult(
      trfConfirmShipmentInstructionRecordReq,
    );
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxForceCompleteConfirmPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxForceCompleteConfirmPropsRef.value.content =
        errorRef.value.response.rMsg;
      const resetData = createMessageBoxForm('message', 'trfRsltForceExpl');
      if (
        'isPrompt' in messageBoxForceCompleteConfirmPropsRef.value &&
        comboBoxResData
      ) {
        messageBoxForceCompleteConfirmPropsRef.value.formItems =
          resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxForceCompleteConfirmPropsRef.value.formItems,
          comboBoxResData.rData.rList,
        );
      }
      openDialog('trfForceCompleteConfirmBeforeAddWarning');
    } else {
      messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
      messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
      messageBoxErrorPropsRef.value.type = 'error';
      openDialog('errorConfirm');
    }
    closeLoading();
    return false;
  }
  if (responseRef.value) {
    messageBoxInfoPropsRef.value.title = responseRef.value.data.rTitle;
    messageBoxInfoPropsRef.value.content = responseRef.value.data.rMsg;
    openDialog('infoConfirm');
  }
  closeLoading();
  return true;
};

const closeAllDialog = () => {
  closeDialog('infoConfirm');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData, '');
};
/**
 * 初期設定
 */
const trfConfirmShipmentInstructionRecordInit = async () => {
  if (props.selectedRows.length === 0) {
    messageBoxErrorPropsRef.value.title = t('Cm.Chr.txtUnselectedData');
    messageBoxErrorPropsRef.value.content = t('Cm.Msg.unselectedData');
    openDialog('errorConfirm');
    return;
  }
  updateDialogChangeFlagRef(false);
  explFormRef.value.formItems = getExplFormItems();
  showLoading();
  // NOTE:単一チェック済みなので確実に単一行。先頭取得する
  const selectedRow = props.selectedRows.at(0)!;
  // 出庫指示明細情報取得_確定用
  const { responseRef, errorRef } =
    await useGetTransferShipmentInstructionDetailResult({
      ...props.privilegesBtnRequestData,
      trfInstGrpNo: selectedRow.trfInstGrpNo,
      trfInstGrpUpdDts: selectedRow.trfInstGrpUpdDts,
    });
  if (errorRef.value) {
    messageBoxErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxErrorPropsRef.value.content = errorRef.value.response.rMsg;
    messageBoxErrorPropsRef.value.type = 'error';
    openDialog('errorConfirm');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    tablePropsData.tableData = responseRef.value.data.rData.trfInstDetailList;
    [trfInstDetailResultFormData] =
      responseRef.value.data.rData.trfInstDetailList;
    setFormModelValueFromApiResponse(
      trfConfirmShipmentInstructionRecordFormRef,
      trfInstDetailResultFormData,
    );
  }

  // 標準コンボボックスデータ取得
  comboBoxResData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'trfRsltExpl',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'TRF_INSTDTL_RSLT' },
      },
      {
        cmbId: 'trfRsltForceExpl',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'TRF_INSTDTL_FORCE' },
      },
    ],
  });
  if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
    setCustomFormComboBoxOptionList(
      trfConfirmShipmentInstructionRecordFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
    setCustomFormComboBoxOptionList(
      explFormRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, trfConfirmShipmentInstructionRecordInit);
</script>
<style lang="scss" scoped></style>
