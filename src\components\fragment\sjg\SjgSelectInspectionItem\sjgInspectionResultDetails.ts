import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

export const getProcessDataInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    // 品目コード
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 品名
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 18,
    },
    // 製造番号
    lotNo: {
      label: { text: t('Sjg.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // ロットアウト品
    verifyReasonNm: {
      label: { text: t('Sjg.Chr.txtVerityReason') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 使用期限
    expiryYmd: {
      label: { text: t('Sjg.Chr.txtExpiryYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
    // 出来高日
    rsltYmd: {
      label: { text: t('Sjg.Chr.txtRsltYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 6,
    },
  });

export const tablePropsDataVerifyResultList: TabulatorTableIF = {
  pageName: 'VerifyResultList',
  pageSize: 20,
  height: '250',
  pagination: false, // ページネーションの表示/非表示
  multiLevelGrouping: false, // Multi Level Grouping データグリッドのグルーピング (Grouping Data)
  dataID: 'matNo',
  showRadio: false,
  searchData: [],
  column: [
    {
      title: 'Sjg.Chr.txtVerifyCat',
      field: 'verifyCatNm',
      width: COLUMN_WIDTHS.SJG.VER_CAT_NM,
    },
    {
      title: 'Sjg.Chr.txtVerifyOdrRslt',
      field: 'verifyOdrRsltNm',
      width: COLUMN_WIDTHS.SJG.VER_ODR_RSLT_NM,
    },
    {
      title: 'Sjg.Chr.txtOdrDevFlg',
      field: 'odrDevFlgNm',
      width: COLUMN_WIDTHS.SJG.ODR_DEV_FLAG_NM,
    },
    { title: 'Sjg.Chr.txtMatNo', field: 'matNo', width: COLUMN_WIDTHS.MAT_NO },
    { title: 'Sjg.Chr.txtMatNm', field: 'matNm', width: COLUMN_WIDTHS.MAT_NM },
    { title: 'Sjg.Chr.txtLotNo', field: 'lotNo', width: COLUMN_WIDTHS.LOT_NO },
    {
      title: 'Sjg.Chr.txtVerifyUserName',
      field: 'verifyUsrNm',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtVerifyYmd',
      field: 'verifyYmd',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtVerifyExpl',
      field: 'verifyExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
  usrGridInfo: {
    colSort: [],
    colHide: [],
  },
};

export const tablePropsDataGMPInfoList: TabulatorTableIF = {
  pageName: 'GMPResultList',
  pageSize: 20,
  height: '200',
  pagination: false, // ページネーションの表示/非表示
  multiLevelGrouping: false, // Multi Level Grouping データグリッドのグルーピング (Grouping Data)
  dataID: 'matNo',
  showRadio: false,

  searchData: [],
  column: [
    {
      title: 'Sjg.Chr.txtVerifyCat',
      field: 'verifyCatNm',
      width: COLUMN_WIDTHS.SJG.VER_CAT_NM,
    },
    { title: 'Sjg.Chr.txtMatNo', field: 'matNo', width: COLUMN_WIDTHS.MAT_NO },
    { title: 'Sjg.Chr.txtMatNm', field: 'matNm', width: COLUMN_WIDTHS.MAT_NM },
    { title: 'Sjg.Chr.txtLotNo', field: 'lotNo', width: COLUMN_WIDTHS.LOT_NO },
    {
      title: 'Sjg.Chr.txtVerifyGmpDocNeedType',
      field: 'gmpDocNeedTypeVerifyNm',
      width: COLUMN_WIDTHS.SJG.VER_GMP_DOC_NEED_TYPE_NM,
    },
    {
      title: 'Sjg.Chr.txtGMPMngNo',
      field: 'gmpMngNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    {
      title: 'Sjg.Chr.txtGMPTitle',
      field: 'gmpTitle',
      width: COLUMN_WIDTHS.SJG.GMP_TITLE,
    },
    {
      title: 'Sjg.Chr.txtGMPDes',
      field: 'gmpDes',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
  usrGridInfo: {
    colSort: [],
    colHide: [],
  },
};
