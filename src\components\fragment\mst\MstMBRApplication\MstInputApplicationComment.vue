<template>
  <!-- 申請コメント入力ダイアログ -->
  <DialogWindow
    :title="$t('Mst.Chr.txtInputApplicationComment')"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'input-application-comment'"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
  >
    <!-- MBR申請情報のテキスト項目表示 -->
    <InfoShow
      class="Util_mt-16"
      :infoShowItems="mbrCreationInfoShowRef.infoShowItems"
      :isLabelVertical="mbrCreationInfoShowRef.isLabelVertical"
    />
    <CustomForm
      class="Util_mt-16"
      :formModel="mbrCreationFormRef.formModel"
      :formItems="mbrCreationFormRef.formItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          mbrCreationFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <!-- MBR申請の確認表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApplicationConfirmVisible"
    :dialogProps="messageBoxApplicationConfirmPropsRef"
    :cancelCallback="() => closeDialog('messageBoxApplicationConfirmVisible')"
    :submitCallback="requestApiApplyMBR"
  />
  <!-- MBR申請完了の表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApplicationCompleteVisible"
    :dialogProps="messageBoxApplicationCompletePropsRef"
    :submitCallback="requestApiApplyMBRFinished"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import onValidateHandler from '@/utils/validateHandler';
import { RuleOption } from '@/types/ValidatorTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import { getDateByType } from '@/utils/index';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { CommonRequestType } from '@/types/HookUseApi/CommonTypes';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import { GetCreationInformationReq } from '@/types/HookUseApi/MstTypes';
import { useGetCreationInformation, useModifyMBRApply } from '@/hooks/useApi';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import {
  getMBRCreationInfoShowItems,
  getMBRCreationFormItems,
  mbrCreationFormRef,
  fromToDate,
} from './mstInputApplicationComment';

/**
 * 多言語
 */
const { t } = useI18n();

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'messageBoxApplicationConfirmVisible'
  | 'messageBoxApplicationCompleteVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  messageBoxApplicationConfirmVisible: false,
  messageBoxApplicationCompleteVisible: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

const mbrCreationInfoShowRef = ref<InfoShowType>({
  infoShowItems: getMBRCreationInfoShowItems(),
  isLabelVertical: true,
});

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

// MBR申請の確認メッセージボックス
const messageBoxApplicationConfirmPropsRef = ref<DialogProps>({
  title: t('Mst.Msg.titleApplicationConfirm'),
  content: t('Mst.Msg.contentApplicationConfirm'),
  type: 'question',
});

// MBR申請完了のメッセージボックス
const messageBoxApplicationCompletePropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

/**
 * 終了日のルールを設定
 * @param {RuleOption[]} rules - ルール
 * @param {string} dataId - データID
 */
const getEndDateRules = (rules: RuleOption[], dataId: string) =>
  rules.map((item: RuleOption) => {
    let rectItem = { ...item };
    if ('type' in item) {
      if (item.type === 'fromToDate') {
        rectItem = {
          ...item,
          validator: fromToDate,
          dataId,
        };
      }
    }
    return rectItem;
  });

// ダイアログ用ボタン設定
const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: async () => {
      const isClose = commonRejectHandler();
      return isClose;
    },
  },
  {
    text: t('Mst.Chr.btnApplication'), // 申請
    type: 'primary',
    size: 'normal',
    clickHandler: async () => {
      // 入力チェック
      const validate =
        mbrCreationFormRef.value.customForm !== undefined &&
        (await mbrCreationFormRef.value.customForm.validate((isValid) => {
          onValidateHandler(isValid);
        }));

      if (validate) {
        // MBR申請の確認メッセージ表示
        openDialog('messageBoxApplicationConfirmVisible');
      }

      return false;
    },
  },
];

// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  mbrNo: string; // MBR番号
  privilegesBtnRequestData: CommonRequestType;
};

const props = defineProps<Props>();
const emit = defineEmits(['submit']);

/**
 * MBR申請処理
 */
const requestApiApplyMBR = async () => {
  // OK押下でメッセージ不要につき閉じる
  closeDialog('messageBoxApplicationConfirmVisible');

  try {
    // 署名ダイアログを表示
    await showSignDialog({
      commonRequestParam: {
        ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
      },
    });
  } catch (error) {
    // 署名ダイアログがキャンセルされた場合の処理
    return;
  }

  showLoading();

  const applyMBRData = {
    ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
    msgboxTitleTxt: messageBoxApplicationConfirmPropsRef.value.title,
    msgboxMsgTxt: messageBoxApplicationConfirmPropsRef.value.content,
    msgboxBtnTxt: t('Cm.Chr.btnOk'),
    mbrNo: props.mbrNo,
    validStYmd: mbrCreationFormRef.value.formModel.validStYmd.toString(),
    validEdYmd: mbrCreationFormRef.value.formModel.validEdYmd.toString(),
    applicationComment:
      mbrCreationFormRef.value.formModel.applicationComment.toString(),
  };

  const { responseRef, errorRef } = await useModifyMBRApply(applyMBRData);

  closeLoading();

  if (errorRef.value) {
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return; // エラーの場合継続処理させない
  }

  if (responseRef.value) {
    // MBR申請完了
    messageBoxApplicationCompletePropsRef.value.title =
      responseRef.value.data.rTitle;
    messageBoxApplicationCompletePropsRef.value.content =
      responseRef.value.data.rMsg;
    openDialog('messageBoxApplicationCompleteVisible');
  }
};

/**
 * MBR申請完了後の処理
 */
const requestApiApplyMBRFinished = async () => {
  // メッセージを閉じる
  closeDialog('messageBoxApplicationCompleteVisible');

  // ダイアログを閉じる
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData, props.mbrNo);
};

// 初回表示、再検索で呼び出される
// 作成情報取得API呼び出し
const requestApiGetCreationInformation = async (
  requestData: GetCreationInformationReq,
) => {
  showLoading();

  // InfoShow初期化
  mbrCreationInfoShowRef.value.infoShowItems = getMBRCreationInfoShowItems();

  // 作成情報を取得
  const { responseRef, errorRef } = await useGetCreationInformation({
    ...props.privilegesBtnRequestData,
    ...requestData,
  });

  if (errorRef.value) {
    closeLoading();
    // NOTE:このAPIはAPI_STATUS_CODE.COMMON_1000(エラー)が返る可能性があるが、処理としては通常のAPIエラーと同様になるため処理分岐不要
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return Promise.reject(); // エラーの場合継続処理させない
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);

  if (responseRef.value) {
    // 作成情報を表示
    Object.entries(responseRef.value.data.rData).forEach(([key, value]) => {
      if (key in mbrCreationInfoShowRef.value.infoShowItems) {
        mbrCreationInfoShowRef.value.infoShowItems[key].infoShowModelValue =
          value?.toString() ?? '';
      }
    });
  }

  closeLoading();

  return Promise.resolve();
};

/**
 * 申請コメント入力ダイアログの初期設定
 */
const mstConsistencyCheckResultsInit = async () => {
  // 作成情報取得のAPI呼び出しと反映
  try {
    const requestData: GetCreationInformationReq = {
      mbrNo: props.mbrNo,
    };
    await requestApiGetCreationInformation(requestData);
  } catch {
    return;
  }

  // ダイアログ終了チェック初期化
  updateDialogChangeFlagRef(false);

  // FormItems初期化
  mbrCreationFormRef.value.formItems = getMBRCreationFormItems();

  // 終了日のルールを設定
  mbrCreationFormRef.value.formItems.validEdYmd.rules = getEndDateRules(
    mbrCreationFormRef.value.formItems.validEdYmd.rules!,
    'validStYmd',
  ) as RuleOption[];

  mbrCreationFormRef.value.formItems.validStYmd.formModelValue = getDateByType(
    new Date().toString(),
    'YYYY/MM/DD',
  );

  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

watch(
  // isClickedの真偽値が切り替わりを監視
  () => props.isClicked,
  async () => {
    // ここに来る条件はisClickedの真偽値が切り替わるたび。(実質ボタン押下のたび)

    // 初期設定呼び出し
    await mstConsistencyCheckResultsInit();
  },
);
</script>
