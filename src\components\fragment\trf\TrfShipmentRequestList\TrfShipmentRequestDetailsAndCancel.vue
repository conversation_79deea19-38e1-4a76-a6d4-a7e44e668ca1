<template>
  <!-- 出庫依頼詳細 -->
  <DialogWindow
    :title="dialogConfig.title"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :onResolve="dialogConfig.onResolve"
    @closeDialog="() => closeDialog('fragmentDialogVisible')"
    :buttons="dialogConfig.buttons"
  >
    <p v-if="props.screenId === SCREENID.TRF_SHIPMENT_REQUEST_CANCEL">
      {{ $t('Trf.Chr.txtShipmentRequestEraseExplain') }}
    </p>
    <BaseHeading
      level="2"
      fontSize="24px"
      class="Util_mb-16"
      :text="$t('Trf.Chr.txtShipmentRequestDetail')"
    />
    <div class="custom-form-container">
      <CustomForm
        :formModel="trfShipmentRequestDetailsAndCancelFormRef.formModel"
        :formItems="trfShipmentRequestDetailsAndCancelFormRef.formItems"
        @visible="
          (v: CustomFormType['customForm']) => {
            trfShipmentRequestDetailsAndCancelFormRef.customForm = v;
          }
        "
      />
    </div>
    <div class="Util_mt-32">
      <TabulatorTable :propsData="tablePropsDialogRef" />
    </div>
  </DialogWindow>
  <!-- 異常 -->
  <MessageBox
    v-if="dialogVisibleRef.singleButton"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="() => closeDialog('singleButton')"
  />
  <!-- 警告 -->
  <MessageBox
    v-if="dialogVisibleRef.trfCancelCompleteWarning"
    :dialogProps="messageBoxTrfCancelCompleteWarningPropsRef"
    :cancelCallback="() => closeDialog('trfCancelCompleteWarning')"
    :submitCallback="
      () =>
        trfCancelCompleteWarningApiHandler(
          messageBoxTrfCancelCompleteWarningPropsRef,
        )
    "
  />
  <!-- 情報 -->
  <MessageBox
    v-if="dialogVisibleRef.infoConfirm"
    :dialogProps="messageBoxSingleButtonRef"
    :submitCallback="closeAllDialog"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  TrfPlanGrpList,
  TransferPlanDetailData,
  DeleteTransferPlanGroupReq,
  GetTransferPlanDetailReq,
} from '@/types/HookUseApi/TrfTypes';
import {
  CommonRequestType,
  ExtendCommonRequestType,
  ComboBoxDataStandardReturnData,
} from '@/types/HookUseApi/CommonTypes';
import { DialogProps } from '@/types/MessageBoxTypes';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import CustomForm from '@/components/parts/CustomForm.vue';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import TabulatorTable from '@/components/model/common/TabulatorTable/TabulatorTable.vue';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import SCREENID from '@/constants/screenId';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import {
  useGetComboBoxDataStandard,
  useGetTransferPlanDetail,
  useDeleteTransferPlanGroup,
} from '@/hooks/useApi';
import { closeLoading, showLoading } from '@/utils/dialog';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import createMessageBoxForm from '@/utils/commentMessageBox';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import {
  trfShipmentRequestDetailsAndCancelFormModel,
  getTrfShipmentRequestDetailsAndCancelFormItems,
  tablePropsData,
  DialogConfig,
} from './trfShipmentRequestDetailsAndCancel';

const trfShipmentRequestDetailsAndCancelFormRef = ref<CustomFormType>({
  formItems: getTrfShipmentRequestDetailsAndCancelFormItems(),
  formModel: trfShipmentRequestDetailsAndCancelFormModel,
});

type Props = {
  isClicked: boolean;
  screenId: string;
  selectedRowData: TrfPlanGrpList | null;
  privilegesBtnRequestData: CommonRequestType;
};

const { t } = useI18n();
const props = defineProps<Props>();
const emit = defineEmits(['submit']);

let transferPlanDetail: TransferPlanDetailData = {
  trfPlanGrpNo: '',
  planGrpYmd: '',
  srcZoneGrpNo: '',
  srcZoneGrpNm: '',
  dstZoneNo: '',
  dstZoneNm: '',
  dsrDts: '',
  usrId: '',
  usrNm: '',
  planGrpExpl: '',
  updDts: '',
  trfPlanList: [],
};
let comboBoxResData: ComboBoxDataStandardReturnData | undefined;

type DialogRefKey =
  | 'singleButton'
  | 'infoConfirm'
  | 'trfCancelCompleteWarning'
  | 'fragmentDialogVisible';
const initialState: InitialDialogState<DialogRefKey> = {
  singleButton: false,
  infoConfirm: false,
  trfCancelCompleteWarning: false,
  fragmentDialogVisible: false,
};
const { dialogVisibleRef, openDialog, closeDialog } = useDialog(initialState);
const tablePropsDialogRef = ref<TabulatorTableIF>({
  ...tablePropsData,
});
const dialogConfig = ref<DialogConfig>({
  title: '',
  buttons: [],
  onResolve: () => {},
});

const dialogButtons: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    clickHandler: () => {
      closeDialog('fragmentDialogVisible');
    },
  },
];

const messageBoxSingleButtonRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'info',
});

const messageBoxForm = createMessageBoxForm('message', 'trfPlanDel');
const messageBoxTrfCancelCompleteWarningPropsRef = ref<DialogProps>({
  title: t('Trf.Msg.titleShipmentRequestErase'),
  content: t('Trf.Msg.contentShipmentRequestErase'),
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

/**
 * 出庫依頼取消確認
 */
const trfCancelCompleteConfirm = () => {
  openDialog('trfCancelCompleteWarning');
  const resetData = createMessageBoxForm('message', 'trfPlanDel');
  if (
    'isPrompt' in messageBoxTrfCancelCompleteWarningPropsRef.value &&
    comboBoxResData
  ) {
    messageBoxTrfCancelCompleteWarningPropsRef.value.formItems =
      resetData.formItems;
    setCustomFormComboBoxOptionList(
      messageBoxTrfCancelCompleteWarningPropsRef.value.formItems,
      comboBoxResData.rData.rList,
    );
  }
  return false;
};

const closeAllDialog = () => {
  closeDialog('infoConfirm');
  closeDialog('fragmentDialogVisible');
  emit('submit', props.privilegesBtnRequestData);
};

/**
 * 出庫依頼取消を実行
 */
const trfCancelCompleteWarningApiHandler = async (
  messageBoxProps: DialogProps,
) => {
  closeDialog('trfCancelCompleteWarning');
  showLoading();
  let deleteTransferPlanGroupData: ExtendCommonRequestType<DeleteTransferPlanGroupReq> =
    {
      ...props.privilegesBtnRequestData,
      trfPlanGrpNo:
        trfShipmentRequestDetailsAndCancelFormRef.value.formModel.trfPlanGrpNo.toString(),
      planGrpUpdDts: transferPlanDetail.updDts,
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      planGrpEraseExpl: '',
    };
  if ('isPrompt' in messageBoxProps && comboBoxResData) {
    deleteTransferPlanGroupData = {
      ...deleteTransferPlanGroupData,
      planGrpEraseExpl: messageBoxProps.formModel.message.toString(),
      msgboxInputCmt: messageBoxProps.formModel.message.toString(),
    };
  }

  const { responseRef, errorRef } = await useDeleteTransferPlanGroup({
    ...deleteTransferPlanGroupData,
  });
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    messageBoxSingleButtonRef.value.title = responseRef.value.data.rTitle;
    messageBoxSingleButtonRef.value.content = responseRef.value.data.rMsg;
    messageBoxSingleButtonRef.value.type = 'info';
    openDialog('infoConfirm');
  }
  closeLoading();
};

/**
 * 初期設定
 */
const trfShipmentRequestDetailsAndCancelInit = async () => {
  if (!props.selectedRowData) return;
  showLoading();
  trfShipmentRequestDetailsAndCancelFormRef.value.formItems =
    getTrfShipmentRequestDetailsAndCancelFormItems();
  if (props.screenId === SCREENID.TRF_SHIPMENT_REQUEST_DETAIL) {
    dialogConfig.value.title = t('Trf.Chr.txtShipmentRequestDetail');
    dialogConfig.value.buttons = dialogButtons;
  }

  if (props.screenId === SCREENID.TRF_SHIPMENT_REQUEST_CANCEL) {
    dialogConfig.value.title = t('Trf.Chr.txtShipmentRequestErase');
    dialogConfig.value.onResolve = trfCancelCompleteConfirm;
    // 標準コンボボックスデータ取得
    comboBoxResData = await useGetComboBoxDataStandard({
      ...props.privilegesBtnRequestData,
      condList: [
        {
          cmbId: 'trfPlanDel',
          condKey: 'm_sys_cmt',
          where: { cmt_cat: 'TRF_PLANGRP_ERASE' },
        },
      ],
    });
    if (comboBoxResData && comboBoxResData.rData.rList.length > 0) {
      if ('formItems' in messageBoxTrfCancelCompleteWarningPropsRef.value) {
        // コメントメッセージボックス選択肢
        setCustomFormComboBoxOptionList(
          messageBoxTrfCancelCompleteWarningPropsRef.value.formItems,
          comboBoxResData.rData.rList,
        );
      }
    }
  }

  // 出庫依頼詳細取得
  const requestData: ExtendCommonRequestType<GetTransferPlanDetailReq> = {
    ...props.privilegesBtnRequestData,
    trfPlanGrpNo: props.selectedRowData.trfPlanGrpNo,
  };
  const { responseRef, errorRef } = await useGetTransferPlanDetail(requestData);
  if (errorRef.value) {
    messageBoxSingleButtonRef.value.title = errorRef.value.response.rTitle;
    messageBoxSingleButtonRef.value.content = errorRef.value.response.rMsg;
    messageBoxSingleButtonRef.value.type = 'error';
    openDialog('singleButton');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    transferPlanDetail = responseRef.value.data.rData;
    tablePropsDialogRef.value.tableData = transferPlanDetail.trfPlanList;
    setFormModelValueFromApiResponse(
      trfShipmentRequestDetailsAndCancelFormRef,
      transferPlanDetail,
    );
  }
  openDialog('fragmentDialogVisible');
  closeLoading();
};

watch(() => props.isClicked, trfShipmentRequestDetailsAndCancelInit);
</script>
<style lang="scss" scoped>
.custom-form-container {
  height: 234px;
  overflow-y: auto;
}
</style>
