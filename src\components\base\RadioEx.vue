<template>
  <el-radio-group
    :modelValue="props.modelValue"
    @change="handleChangeModelValue"
    :class="{ 'horizontal-class': !props.isHorizontal }"
  >
    <el-radio
      :class="radioClass"
      v-for="(item, index) in props.optionsData.value"
      :key="index"
      :label="item"
      border
      :disabled="props.disabled"
      :value="item"
      >{{ optionsData.label[index] }}</el-radio
    >
  </el-radio-group>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { RadioExProps } from '@/types/RadioExTypes';
import SCSS from '@/constants/scssVariables';

const props = withDefaults(defineProps<RadioExProps>(), {
  size: 'small',
  width: SCSS.widthSmall,
  disabled: false,
  isHorizontal: false,
});

const radioClass = computed(() => `radio-ex_${props.size}`);

const emit = defineEmits(['update:modelValue']);
const handleChangeModelValue = (value: string) => {
  emit('update:modelValue', value);
};
</script>

<style lang="scss" scoped>
$namespace: 'radio-ex';
$widthVal: v-bind('props.width');

@mixin setSizeMixin($width, $height) {
  width: $width !important;
  height: $height !important;
}

.#{$namespace} {
  &_custom {
    @include setSizeMixin($widthVal, $height);
  }
  &_large {
    @include setSizeMixin($widthLarge, $height);
  }
  &_middle {
    @include setSizeMixin($widthMiddle, $height);
  }
  &_small {
    @include setSizeMixin($widthSmall, $height);
  }
}
.el-radio {
  margin-right: 0;
  --el-radio-font-size: 16px;
}

.el-radio.is-bordered {
  border-color: $gray447;
}
.el-radio.is-bordered.is-checked {
  background-color: $blue727;
  border-color: $blue100;
}

:deep(.el-radio__input.is-checked + .el-radio__label) {
  color: $blue100;
}

:deep(.el-radio__input .el-radio__inner) {
  border-color: $gray447;
}
:deep(.el-radio__input.is-checked .el-radio__inner) {
  background-color: $blue100;
  border-color: $blue100;
}
.el-radio-group {
  gap: 15px 30px;
  width: 100%;
}
.horizontal-class {
  display: inline-grid;
}
</style>
