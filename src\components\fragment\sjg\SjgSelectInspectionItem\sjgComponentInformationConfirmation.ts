import i18n from '@/constants/lang';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import CONST_FLAGS from '@/constants/flags';

const { t } = i18n.global;

export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  height: '280px',
  dataID: 'uniqueKey',
  rowHeight: 35,
  hideCheckboxTitleFormatter: true,
  showCheckbox: {
    show: true,
    condition: 'verifyBomFlgNo',
    conditionValue: Number(CONST_FLAGS.SJG.VERIFY_BOM_FLG.CHECKED),
    allAllowed: false,
  },
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 100, // ボタン列幅
    condition: 'disabledBtnFlg',
    conditionValue: Number(CONST_FLAGS.SJG.VERIFY_BOM_FLG.CHECKED),
    btnProps: {
      text: t('Cm.Chr.btnDetail'),
      size: 'small',
      type: 'secondary',
    },
  },
  column: [
    {
      title: 'Sjg.Chr.txtVerifyBomFlg',
      field: 'verifyBomFlgNm',
      width: COLUMN_WIDTHS.SJG.VER_BOM_FLG,
    },
    {
      title: 'Sjg.Chr.txtOdrNo',
      field: 'odrNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    {
      title: 'Sjg.Chr.txtOdrCat',
      field: 'odrCatNm',
      width: COLUMN_WIDTHS.SJG.ODR_CAT,
    },
    {
      title: 'Sjg.Chr.txtMatNo',
      field: 'bomMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Sjg.Chr.txtMatNm',
      field: 'bomMatNm',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    {
      title: 'Sjg.Chr.txtShtNm',
      field: 'bomShtNm',
      width: COLUMN_WIDTHS.SJG.SHT_NM,
    },
    {
      title: 'Sjg.Chr.txtBomLotNo',
      field: 'bomLotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    {
      title: 'Sjg.Chr.txtAogAttExistFlgNm',
      field: 'aogAttExistFlgNm',
      width: COLUMN_WIDTHS.SJG.AOG_ATT_EXIST_FLG_NM,
    },
    {
      title: 'Sjg.Chr.txtLogLotStsCheckNm',
      field: 'logLotStsCheckNm',
      width: COLUMN_WIDTHS.SJG.CHK_NM,
    },
    {
      title: 'Sjg.Chr.txtlotLotStsNm',
      field: 'lotLotStsNm',
      width: COLUMN_WIDTHS.SJG.LOT_STS,
    },
    {
      title: 'Sjg.Chr.txtLogExpiryDtsCheckNm',
      field: 'logExpiryYmdCheckNm',
      width: COLUMN_WIDTHS.SJG.CHK_NM,
    },
    {
      title: 'Sjg.Chr.txtLotExpiryDts',
      field: 'lotExpiryYmd',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtLogShelfLifeDtsCheckNm',
      field: 'logShelfLifeYmdCheckNm',
      width: COLUMN_WIDTHS.SJG.CHK_NM,
    },
    {
      title: 'Sjg.Chr.txtLogWgtShelfLifeDtsCheckNm',
      field: 'logWgtYmdCheckNm',
      width: COLUMN_WIDTHS.SJG.CHK_NM,
    },
    {
      title: 'Sjg.Chr.txtLogConsDeadlineCheckNm',
      field: 'logConsDeadlineCheckNm',
      width: COLUMN_WIDTHS.SJG.CHK_NM,
    },
    {
      title: 'Sjg.Chr.txtPrdMatNo',
      field: 'prdMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Sjg.Chr.txtPrdMatNm',
      field: 'prdMatNm',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    {
      title: 'Sjg.Chr.txtPrdShtNm',
      field: 'prdShtNm',
      width: COLUMN_WIDTHS.SJG.SHT_NM,
    },
    {
      title: 'Sjg.Chr.txtPrdLotNo',
      field: 'prdLotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    {
      title: 'Sjg.Chr.txtPrdRsltDts',
      field: 'prdRsltYmd',
      hozAlign: 'center',
      formatter: 'date',
      dateFormatter: 'YYYY/MM/DD',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: '',
      field: 'disabledBtnFlg',
      formatter: 'btn',
      hozAlign: 'center',
    },
    { title: '', field: 'uniqueKey', hidden: true },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
  cellColor: {
    color: 'red',
  },
};

export default tablePropsData;
