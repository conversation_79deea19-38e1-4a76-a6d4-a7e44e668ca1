import i18n from '@/constants/lang';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import { rules } from '@/utils/validator';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

const { t } = i18n.global;

export const getTrfConfirmShipmentInstructionRecordFormItems: () => CustomFormType['formItems'] =
  () => ({
    trfInstGrpNo: {
      label: { text: t('Trf.Chr.txtTrfInstGrpNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    prYmd: {
      label: { text: t('Trf.Chr.txtDsrDts') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    instUsr: {
      label: { text: t('Trf.Chr.txtInstUsr') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    prtDts: {
      label: { text: t('Trf.Chr.txtPrtDts') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    planNo: {
      label: { text: t('Trf.Chr.txtShipmentRequestNo') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    planUsr: {
      label: { text: t('Trf.Chr.txtPlanUsr') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
    dstZoneNm: {
      label: { text: t('Trf.Chr.txtDstZoneNm') },
      formModelValue: '',
      formRole: 'textBox',
      props: { disabled: true, size: 'small' },
      span: 12,
    },
  });

export const trfConfirmShipmentInstructionRecordFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getTrfConfirmShipmentInstructionRecordFormItems());

export const getExplFormItems: () => CustomFormType['formItems'] = () => ({
  expl: {
    label: { text: t('Trf.Chr.txtComment') },
    formModelValue: '',
    rules: [rules.length(64, t('Cm.Chr.txtLength', [64]))],
    formRole: 'textComboBox',
    props: { clearable: true },
    selectOptions: [],
    cmbId: 'trfRsltExpl',
  },
});

export const explFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getExplFormItems());

export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'matNo',
  height: '163px',
  column: [
    {
      title: 'Trf.Chr.txtInstStsNm',
      field: 'instStsNm',
      width: COLUMN_WIDTHS.TRF.INST_STS_NM,
    },
    {
      title: 'Trf.Chr.txtSrcZoneNm',
      field: 'srcZoneNm',
      width: COLUMN_WIDTHS.ZONE_NM,
    },
    { title: 'Trf.Chr.txtMatNo', field: 'matNo', width: COLUMN_WIDTHS.MAT_NO },
    { title: 'Trf.Chr.txtMatNm', field: 'matNm', width: COLUMN_WIDTHS.MAT_NM },
    {
      title: 'Trf.Chr.txtManageNo',
      field: 'lotNo',
      width: COLUMN_WIDTHS.LOT_NO,
    },
    { title: 'Trf.Chr.txtEdNo', field: 'edNo', width: COLUMN_WIDTHS.ED_NO },
    {
      title: 'Trf.Chr.txtTrfQty',
      field: 'planQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.TRF.PLAN_QTY,
    },
    {
      title: 'Trf.Chr.txtInstQty',
      field: 'instQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.INST_QTY,
    },
    {
      title: 'Trf.Chr.txtRsltQty',
      field: 'rsltQty',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.RSLT_QTY,
    },
    {
      title: 'Trf.Chr.txtUnitNm',
      field: 'unitNm',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Trf.Chr.txtComment',
      field: 'fnpckHtExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
};
