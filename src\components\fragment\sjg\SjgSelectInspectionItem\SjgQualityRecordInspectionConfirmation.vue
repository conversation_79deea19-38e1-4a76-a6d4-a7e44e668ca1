<template>
  <!-- 品質記録照査確認ダイアログ -->
  <!-- 見出し 品質記録照査確認 -->
  <DialogWindow
    :title="dialogTitle"
    :dialogVisible="dialogVisibleRef.fragmentDialogVisible"
    :buttons="dialogButtons"
    :class="'odr-regist-rebatch-instruction'"
    @closeDialog="() => handleCloseFragmentDialog()"
  >
    <!-- 品目情報-->
    <BaseHeading level="2" :text="$t('Sjg.Chr.txtInfoItem')" fontSize="24px" />
    <div>
      <!-- 品目情報の見出し+テキスト項目表示 -->
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="processDataInfoShowRef.infoShowItems"
        :isLabelVertical="processDataInfoShowRef.isLabelVertical"
        fontSizeLabel="12px"
        fontSizeContent="16px"
      />
    </div>
    <!-- 品質記録照査情報 -->
    <BaseHeading
      level="2"
      :text="$t('Sjg.Chr.txtQualityRecordInspectionInfo')"
      fontSize="24px"
      class="Util_mt-48"
    />
    <div>
      <!-- 品質記録照査情報の見出し+テキスト項目表示 -->
      <InfoShow
        class="Util_mt-16"
        :infoShowItems="confirmDataInfoShowRef.infoShowItems"
        :isLabelVertical="confirmDataInfoShowRef.isLabelVertical"
        fontSizeLabel="12px"
        fontSizeContent="16px"
      />
    </div>

    <CustomForm
      class="Util_mt-16"
      :triggerRendering="customFormRenderingTriggerRef"
      :formModel="getCustomFormRef.formModel"
      :formItems="getCustomFormRef.formItems"
      @selectedItem="updateFormItems"
      @visible="
        (v: CustomFormType['customForm']) => {
          getCustomFormRef.customForm = v;
        }
      "
      @changeFormModel="updateDialogChangeFlagRef"
    />
  </DialogWindow>
  <!-- APIのエラー表示 -->
  <MessageBox
    v-if="dialogVisibleRef.messageBoxApiErrorVisible"
    :dialogProps="messageBoxApiErrorPropsRef"
    :submitCallback="() => closeDialog('messageBoxApiErrorVisible')"
  />
  <MessageBox
    v-if="dialogVisibleRef.verifyStart"
    :dialogProps="messageBoxVerifyStartPropsRef"
    :cancelCallback="() => closeDialog('verifyStart')"
    :submitCallback="
      () => closeVerifyStartDialog(messageBoxVerifyStartPropsRef)
    "
  />
  <MessageBox
    v-if="dialogVisibleRef.sjgInfoValidCheckError"
    :dialogProps="messageBoxSjgInfoValidCheckErrorRef"
    :submitCallback="() => closeSjgInfoValidCheckError()"
  />
</template>
<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { rules } from '@/utils/validator';
import SCREENID from '@/constants/screenId';
import onValidateHandler from '@/utils/validateHandler';
import BaseHeading from '@/components/base/BaseHeading.vue';
import { DialogWindowProps } from '@/types/DialogWindowTypes';
import DialogWindow from '@/components/parts/DialogWindow.vue';
import useDialog, { InitialDialogState } from '@/hooks/useDialog';
import { closeLoading, showLoading } from '@/utils/dialog';
import MessageBox from '@/components/parts/MessageBox/MessageBox.vue';
import CustomForm from '@/components/parts/CustomForm.vue';
import InfoShow from '@/components/parts/InfoShow.vue';
import { DialogProps } from '@/types/MessageBoxTypes';
import { InfoShowType } from '@/types/InfoShowTypes';
import CONST from '@/constants/utils';
import showSignDialog from '@/components/model/SignDialog/SignDialog';
import createMessageBoxForm from '@/utils/commentMessageBox';
import {
  useGetComboBoxDataStandard,
  useGetQualityRecord,
  useCheckVerifyStatus,
  useModifyQualityRecVerifyStart,
  useModifyQualityRecordVerifyFinish,
  useCheckValidVerify,
} from '@/hooks/useApi';
import { setCustomFormComboBoxOptionList } from '@/utils/comboBoxOptionList';
import { CustomFormType } from '@/types/CustomFormTypes';
import {
  SelectInspectionItemData,
  VerifyItemList,
  ModifyQualityRecVerifyStartReq,
} from '@/types/HookUseApi/SjgTypes';
import {
  CommonRequestType,
  ComboBoxDataStandardReturnData,
  ExtendCommonRequestType,
} from '@/types/HookUseApi/CommonTypes';
import { setFormModelValueFromApiResponse } from '@/utils/customForm';
import CONST_FLAGS from '@/constants/flags';
import {
  getProcessDataInfoShowItems,
  getConfirmDataInfoShowItems,
  inspectionCompletionFormModel,
  getInspectionCompletionFormItems,
} from './sjgQualityRecordInspectionConfirmation';

// [W181310]品質記録照査確認
/**
 * 多言語
 */
const { t } = useI18n();
// 親から渡す情報群
type Props = {
  isClicked: boolean; // ダイアログ起動条件。真偽値が切り替わるたびに表示される
  privilegesBtnRequestData: CommonRequestType;
  selectedInspectionItemData: SelectInspectionItemData | null;
  selectedRowData: VerifyItemList | null;
  screenId: string;
  comboBoxStandardReturnData: ComboBoxDataStandardReturnData | undefined;
};

/**
 *
 * 警告要否フラグ
 * - UNNECESSARY: 警告が不要であることを表します（値: 0）。
 * - NECESSITY: 警告が必要であることを表します（値: 1）：デフォルト値。
 */
const WARNING_NECESSITY_FLAG = {
  UNNECESSARY: 0,
  NECESSITY: 1,
} as const;

const props = defineProps<Props>();
const router = useRouter();
let dialogTitle: string = t(
  'Sjg.Chr.txtConfirmQualityRecordInspectionConfirmation',
);
let comboBoxDataStandardReturnData: ComboBoxDataStandardReturnData | undefined;

// ダイアログの表示切替用定義
type DialogRefKey =
  | 'fragmentDialogVisible'
  | 'messageBoxApiErrorVisible'
  | 'verifyStart'
  | 'sjgInfoValidCheckError';
const initialState: InitialDialogState<DialogRefKey> = {
  fragmentDialogVisible: false,
  messageBoxApiErrorVisible: false,
  verifyStart: false,
  sjgInfoValidCheckError: false,
};

const {
  dialogVisibleRef,
  openDialog,
  closeDialog,
  updateDialogChangeFlagRef,
  commonRejectHandler,
} = useDialog(initialState);

// APIエラーのメッセージボックス
const messageBoxApiErrorPropsRef = ref<DialogProps>({
  title: '', // レスポンスで上書きする
  content: '', // レスポンスで上書きする
  isSingleBtn: true,
  type: 'error',
});

const messageBoxForm = createMessageBoxForm('message', 'cmtSjgVeriOther');
const messageBoxVerifyStartPropsRef = ref<DialogProps>({
  title: '',
  content: '',
  isPrompt: true,
  formModel: messageBoxForm.formModel,
  formItems: messageBoxForm.formItems,
  type: 'warning',
});

// 照査データ有効チェックエラーのメッセージ
const messageBoxSjgInfoValidCheckErrorRef = ref<DialogProps>({
  title: '',
  content: '',
  isSingleBtn: true,
  type: 'error',
});

const getCustomFormRef = ref<CustomFormType>({
  formItems: getInspectionCompletionFormItems(props.screenId),
  formModel: inspectionCompletionFormModel,
});

const processDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getProcessDataInfoShowItems(),
  isLabelVertical: true,
});

// 品質記録照査情報
const confirmDataInfoShowRef = ref<InfoShowType>({
  infoShowItems: getConfirmDataInfoShowItems(),
  isLabelVertical: true,
});
const customFormRenderingTriggerRef = ref(false);
const emit = defineEmits(['submit']);

const handleCloseFragmentDialog = () => {
  closeDialog('fragmentDialogVisible');
  if (props.screenId === SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION)
    emit('submit', props.privilegesBtnRequestData);
};

const checkValidVerifyHandler = async () => {
  const apiRequestData: ExtendCommonRequestType<{ lotSid: string }> = {
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  };
  // 照査情報有効チェック
  const { errorRef } = await useCheckValidVerify(apiRequestData);
  if (errorRef.value) {
    closeLoading();
    messageBoxSjgInfoValidCheckErrorRef.value.title =
      errorRef.value.response.rTitle;
    messageBoxSjgInfoValidCheckErrorRef.value.content =
      errorRef.value.response.rMsg;
    openDialog('sjgInfoValidCheckError');
    return false;
  }
  return true;
};

/**
 * 品質記録照査確認の完了
 */
const modifyVerifyFinishAPI = async () => {
  // 品質記録照査確認の完了チェック
  const { responseRef, errorRef } = await useModifyQualityRecordVerifyFinish({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
    verifyCat: '2',
    odrRslt: getCustomFormRef.value.formModel.odrRslt.toString(),
    verifyExpl: getCustomFormRef.value.formModel.rsltVerifyExpl.toString(),
  });
  if (errorRef.value) {
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return;
  }
  if (responseRef.value) {
    // 品質記録照査確認ダイアログを閉める
    handleCloseFragmentDialog();
  }
};
/**
 * 照査完了ボタンの動作
 */
const clickConfirmBtn = async () => {
  //
  const validate =
    getCustomFormRef.value.customForm !== undefined &&
    (await getCustomFormRef.value.customForm.validate((isValid) => {
      onValidateHandler(isValid);
    }));

  if (!validate) {
    return;
  }
  if (!(await checkValidVerifyHandler())) {
    return;
  }
  showLoading();
  // 照査可否チェック
  const { responseRef, errorRef } = await useCheckVerifyStatus({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
    verifyCat: '2',
  });
  if (errorRef.value) {
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    closeLoading();
    return;
  }
  if (responseRef.value) {
    // 顔認証を行うための画面(W99A200)から顔認証を行う必要がある
    // 認証が成功した場合、下記の処理を行う
    try {
      // 署名ダイアログを表示
      await showSignDialog({
        commonRequestParam: {
          ...props.privilegesBtnRequestData, // 必要な共通リクエストパラメータを設定
        },
      });
    } catch (error) {
      return;
    }
    await modifyVerifyFinishAPI();
  }
  closeLoading();
};

const buttonsList: DialogWindowProps['buttons'] = [
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    disabled: false,
    // NOTE:クリック時は自身を閉じるのみ。これはcloseDialogで行うためclickHandlerを用意していない。
  },
  {
    text: t('Cm.Chr.btnCancel'),
    type: 'secondary',
    size: 'normal',
    disabled: false,
    clickHandler: commonRejectHandler,
  },
  {
    text: t('Sjg.Chr.txtConfirmCompletion'),
    type: 'primary',
    size: 'normal',
    clickHandler: () => {
      clickConfirmBtn();
      return false;
    },
  },
];
// NOTE:このダイアログはキャンセルのみ。デフォルト挙動を上書きするための定義。
// ダイアログ用ボタン設定
const dialogButtons = ref<DialogWindowProps['buttons']>(buttonsList);
// フッターの確認完了ボタンを非表示にする。
const setFootBtnDisable = () => {
  // 照査完了後の確認の場合
  let dialogButtonsTemp: DialogWindowProps['buttons'] = [];
  // 照査状態取得: IN：(Initial) 未実施、ST：(Start) 照査中、FN:（Finish）照査完了
  if (
    props.selectedRowData?.verifyCatSts === 'FN' ||
    props.screenId ===
      SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION_CONFIRMATION
  ) {
    dialogButtonsTemp = buttonsList.slice(0, 1);
  } else {
    // YES
    dialogButtonsTemp = buttonsList.slice(1, buttonsList.length);
  }
  dialogButtons.value = dialogButtonsTemp;
};

const closeSjgInfoValidCheckError = async () => {
  closeDialog('sjgInfoValidCheckError');
  router.push({
    name: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
    state: {
      routerName: SCREENID.SJG_INSPECTION_CANDIDATE_LIST,
      searchConditionData: window.history.state.conditionData,
      tableSearchData: window.history.state.tableSearchData,
    },
  });
};

const qualityRecordInspectionConfirmationInit = async () => {
  if (!props.selectedRowData) return;
  if (!props.selectedInspectionItemData) return;
  updateDialogChangeFlagRef(false);
  Object.entries(props.selectedInspectionItemData).forEach(([key, value]) => {
    if (key in processDataInfoShowRef.value.infoShowItems) {
      processDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });

  // 品質記録照査情報レイアウト用初期値設定
  Object.entries(props.selectedInspectionItemData).forEach(([key, value]) => {
    if (key in confirmDataInfoShowRef.value.infoShowItems) {
      confirmDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
        value?.toString() ?? '';
    }
  });
  setFootBtnDisable();

  // コメント必須チェック設定（照査結果がNGまたは未実施の場合は必須）
  if (
    getCustomFormRef.value.formItems.odrRslt.formModelValue !== 'FL' &&
    getCustomFormRef.value.formItems.odrRslt.formModelValue !== 'NV' &&
    getCustomFormRef.value.formItems.odrRslt.formModelValue !== ''
  ) {
    getCustomFormRef.value.formItems.rsltVerifyExpl.rules = [
      rules.length(64, t('Cm.Chr.txtLength', [64])),
    ];
    getCustomFormRef.value.formItems.rsltVerifyExpl.tags = [];
    customFormRenderingTriggerRef.value = !customFormRenderingTriggerRef.value;
  }

  if (
    comboBoxDataStandardReturnData &&
    comboBoxDataStandardReturnData.rData.rList.length > 0
  ) {
    setCustomFormComboBoxOptionList(
      getCustomFormRef.value.formItems,
      comboBoxDataStandardReturnData.rData.rList,
    );
  }
  // 問題なければダイアログ起動する
  openDialog('fragmentDialogVisible');
};

// 初期処理で呼び出される
// 品質記録照査情報取得APIリクエストとレスポンス情報を格納
const requestApiGetVerifyResult = async () => {
  // 品質記録照査情報取得のAPIを行う。
  const { responseRef, errorRef } = await useGetQualityRecord({
    ...props.privilegesBtnRequestData,
    lotSid: props.selectedInspectionItemData!.lotSid,
  });

  if (errorRef.value) {
    closeLoading();
    // APIエラー用メッセージボックス起動
    messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
    messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
    openDialog('messageBoxApiErrorVisible');
    return false; // エラーの場合継続処理させない
  }
  if (responseRef.value) {
    // ダイアログ表示初期値として、レスポンス情報を格納
    const initResponseData = {
      ...responseRef.value.data.rData,
      odrRslt:
        responseRef.value.data.rData.odrRslt === 'IN'
          ? ''
          : responseRef.value.data.rData.odrRslt,
    };
    if (
      props.screenId ===
      SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION_CONFIRMATION
    ) {
      setFormModelValueFromApiResponse(getCustomFormRef, initResponseData);
    }

    if (initResponseData) {
      // 品質記録照査情報レイアウト用初期値設定
      Object.entries(initResponseData).forEach(([key, value]) => {
        if (key in confirmDataInfoShowRef.value.infoShowItems) {
          confirmDataInfoShowRef.value.infoShowItems[key].infoShowModelValue =
            value?.toString() ?? '';
        }
      });
    }
    await qualityRecordInspectionConfirmationInit();
  }
  closeLoading();
  return true;
};

const modifyQualityRecVerifyStart = async (
  messageBoxProps: DialogProps,
  warningNecessityFlg: number = WARNING_NECESSITY_FLAG.NECESSITY,
) => {
  // 品質記録照査情報取得のAPIを行う。
  let modifyQualityRecVerifyStartReq: ExtendCommonRequestType<ModifyQualityRecVerifyStartReq> =
    {
      ...props.privilegesBtnRequestData,
      lotSid: props.selectedInspectionItemData!.lotSid,
      verifyExpl: '',
      msgboxTitleTxt: messageBoxProps.title,
      msgboxMsgTxt: messageBoxProps.content,
      msgboxBtnTxt: t('Cm.Chr.btnOk'),
      warningNecessityFlg,
    };
  if (
    'isPrompt' in messageBoxVerifyStartPropsRef.value &&
    warningNecessityFlg === WARNING_NECESSITY_FLAG.UNNECESSARY &&
    props.comboBoxStandardReturnData
  ) {
    modifyQualityRecVerifyStartReq = {
      ...modifyQualityRecVerifyStartReq,
      msgboxInputCmt:
        messageBoxVerifyStartPropsRef.value.formModel.message.toString(),
      verifyExpl:
        messageBoxVerifyStartPropsRef.value.formModel.message.toString(),
    };
  }

  const { responseRef, errorRef } = await useModifyQualityRecVerifyStart(
    modifyQualityRecVerifyStartReq,
  );
  if (errorRef.value) {
    if (errorRef.value.response.rCode === CONST.API_STATUS_CODE.COMMON_1001) {
      messageBoxVerifyStartPropsRef.value.title =
        errorRef.value.response.rTitle;
      messageBoxVerifyStartPropsRef.value.content =
        errorRef.value.response.rMsg;
      const resetData = createMessageBoxForm('message', 'cmtSjgVeriOther');
      if (
        'isPrompt' in messageBoxVerifyStartPropsRef.value &&
        props.comboBoxStandardReturnData
      ) {
        messageBoxVerifyStartPropsRef.value.formItems = resetData.formItems;
        setCustomFormComboBoxOptionList(
          messageBoxVerifyStartPropsRef.value.formItems,
          props.comboBoxStandardReturnData.rData.rList,
        );
      }
      closeLoading();
      openDialog('verifyStart');
    } else {
      closeLoading();
      // APIエラー用メッセージボックス起動
      messageBoxApiErrorPropsRef.value.title = errorRef.value.response.rTitle;
      messageBoxApiErrorPropsRef.value.content = errorRef.value.response.rMsg;
      openDialog('messageBoxApiErrorVisible');
    }
  }
  if (responseRef.value) {
    await requestApiGetVerifyResult();
  }
  closeLoading();
  return true; // エラーの場合継続処理させない
};

const closeVerifyStartDialog = async (messageBoxProps: DialogProps) => {
  closeDialog('verifyStart');
  // 製造記録照査開始
  await modifyQualityRecVerifyStart(
    messageBoxProps,
    WARNING_NECESSITY_FLAG.UNNECESSARY,
  );
  return true;
};

/**
 * verifyExplのルールの更新を行う
 */
const updateFormItems = (fieldId: string) => {
  if (
    props.screenId ===
    SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION_CONFIRMATION
  )
    return;
  // フォーカスアウト時の動作
  if (fieldId === 'odrRslt') {
    if (getCustomFormRef.value.formModel.odrRslt === 'PS') {
      getCustomFormRef.value.formItems.rsltVerifyExpl.tags = [];
      getCustomFormRef.value.formItems.rsltVerifyExpl.rules = [];
      getCustomFormRef.value.customForm?.clearValidate('rsltVerifyExpl');
    } else {
      getCustomFormRef.value.formItems.rsltVerifyExpl.tags = [
        { text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' },
      ];
      getCustomFormRef.value.formItems.rsltVerifyExpl.rules = [
        rules.required('textComboBox'),
        rules.length(64, t('Cm.Chr.txtLength', [64])),
      ];
    }
    customFormRenderingTriggerRef.value = !customFormRenderingTriggerRef.value;
  }
};

/**
 * 品質記録照査確認ダイアログの初期設定
 */
const sjgQualityRecordInspectionConfirmationInit = async () => {
  if (!props.selectedRowData) return;
  if (!props.selectedInspectionItemData) return;

  confirmDataInfoShowRef.value.infoShowItems = getConfirmDataInfoShowItems();
  processDataInfoShowRef.value.infoShowItems = getProcessDataInfoShowItems();
  // 品質記録照査確認情報レイアウト用初期値設定
  getCustomFormRef.value.formItems = getInspectionCompletionFormItems(
    props.screenId,
  );
  showLoading();
  const cmbOdrRsltWhereKeyAndVal =
    props.selectedInspectionItemData.verifyReason ===
      CONST_FLAGS.SJG.LOT_OUT_STATUS.ONLY_LOT_OUT_ITEM ||
    props.selectedInspectionItemData.limsVerifyRecvMatFlg ===
      CONST_FLAGS.SJG.LIMS_VERIFY_RECV_MAT_FLG.NOT_RECEIVED_ITEM
      ? { cd_id: 'ODR_RSLT' }
      : { cd_id: 'ODR_RSLT', cd_val: 'PS,FL' };

  comboBoxDataStandardReturnData = await useGetComboBoxDataStandard({
    ...props.privilegesBtnRequestData,
    condList: [
      {
        cmbId: 'cmtSjgVeriQuality',
        condKey: 'm_sys_cmt',
        where: { cmt_cat: 'SJG_VERIFY_QUALITY' },
      },
      {
        cmbId: 'cmbOdrRslt',
        condKey: 'm_mp_cd',
        where: cmbOdrRsltWhereKeyAndVal,
      },
    ],
  });

  if (props.screenId === SCREENID.SJG_QUALITY_RECORD_INSPECTION_CONFIRMATION) {
    dialogTitle = t('Sjg.Chr.txtQualityRecordInspectionConfirmation');
    await modifyQualityRecVerifyStart(messageBoxVerifyStartPropsRef.value);
    return;
  }
  await requestApiGetVerifyResult();
  closeLoading();
};

watch(() => props.isClicked, sjgQualityRecordInspectionConfirmationInit);
</script>
<style lang="scss" scoped></style>
