import { ref } from 'vue';
import i18n from '@/constants/lang';
import { InfoShowType } from '@/types/InfoShowTypes';
import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import { CustomFormType } from '@/types/CustomFormTypes';
import { createFormModelByFormItems } from '@/utils/customForm';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';
import { rules } from '@/utils/validator';

const { t } = i18n.global;

export const getSjgConfirmInspectionResultsInfoShowItems: () => InfoShowType['infoShowItems'] =
  () => ({
    matNo: {
      label: { text: t('Sjg.Chr.txtMatNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 5,
    },
    shtNm: {
      label: { text: t('Sjg.Chr.txtShtNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 5,
    },
    matNm: {
      label: { text: t('Sjg.Chr.txtMatNm') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 14,
    },
    lotNo: {
      label: { text: t('Sjg.Chr.txtLotNo') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 4,
    },
    rsltQty: {
      label: { text: t('Sjg.Chr.txtRsltQty') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
    invQty: {
      label: { text: t('Sjg.Chr.txtInventoryQuantity') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 3,
    },
    unitNm: {
      label: { text: t('Sjg.Chr.txtUnit') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 2,
    },
    rsltYmd: {
      label: { text: t('Sjg.Chr.txtRsltYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 4,
    },
    expiryYmd: {
      label: { text: t('Sjg.Chr.txtExpiryYmd') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 4,
    },
    expiryTxt: {
      label: { text: t('Sjg.Chr.txtExpiryTxt') },
      infoShowModelValue: '',
      infoShowRole: 'text',
      span: 4,
    },
  });
export const tablePropsData: TabulatorTableIF = {
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'lotNo',
  height: '165px',
  column: [
    { title: 'Sjg.Chr.txtMatNo', field: 'matNo', width: COLUMN_WIDTHS.MAT_NO },
    { title: 'Sjg.Chr.txtMatNm', field: 'matNm', width: COLUMN_WIDTHS.MAT_NM },
    {
      title: 'Sjg.Chr.txtShtNm',
      field: 'shtNm',
      width: COLUMN_WIDTHS.SJG.SHT_NM,
    },
    { title: 'Sjg.Chr.txtLotNo', field: 'lotNo', width: COLUMN_WIDTHS.LOT_NO },
    {
      title: 'Sjg.Chr.txtVerifyCat',
      field: 'verifyCatNm',
      width: COLUMN_WIDTHS.SJG.VER_CAT_NM,
    },
    {
      title: 'Sjg.Chr.btnInspectionResults',
      field: 'verifyOdrRsltNm',
      width: COLUMN_WIDTHS.SJG.VER_ODR_RSLT_NM,
    },
    {
      title: 'Sjg.Chr.txtOdrDevFlg',
      field: 'odrDevFlgNm',
      width: COLUMN_WIDTHS.SJG.ODR_DEV_FLAG_NM,
    },
    {
      title: 'Sjg.Chr.txtVerifyUserName',
      field: 'verifyUsrNm',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtVerifyYmdhs',
      field: 'verifyYmd',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMM,
    },
    {
      title: 'Sjg.Chr.txtVerifyExpl',
      field: 'verifyExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
  cellColor: {
    color: 'red',
  },
};

export const tablePropsDataGmpContent = ref<TabulatorTableIF>({
  pageSize: 20,
  pagination: false, // ページネーションの表示/非表示
  dataID: 'lotNo',
  height: '165px',
  btnColumn: {
    frozen: true, // 凍結列
    columnWidth: 100, // ボタン列幅
    condition: 'recordChg',
    conditionValue: 0,
    btnProps: {
      text: t('Sjg.Chr.txtGmpDocNeedTypeChange'),
      size: 'small',
      type: 'secondary',
    },
  },
  column: [
    {
      title: '',
      field: 'gmpDocNeedTypeVerify',
      hidden: true,
    },
    {
      title: '',
      field: 'gmpDocNeedTypeRelease',
      hidden: true,
    },
    {
      title: 'Sjg.Chr.txtGmpDocNeedTypeVerify',
      field: 'gmpDocNeedTypeReleaseNm',
      width: COLUMN_WIDTHS.SJG.VER_GMP_DOC_NEED_TYPE_NM,
    },
    {
      title: 'Sjg.Chr.txtVerifyGmpDocNeedTypeVerify',
      field: 'gmpDocNeedTypeVerifyNm',
      width: COLUMN_WIDTHS.SJG.VER_GMP_DOC_NEED_TYPE_VER_NM,
    },
    { title: 'Sjg.Chr.txtMatNo', field: 'matNo', width: COLUMN_WIDTHS.MAT_NO },
    { title: 'Sjg.Chr.txtMatNm', field: 'matNm', width: COLUMN_WIDTHS.MAT_NM },
    {
      title: 'Sjg.Chr.txtShtNm',
      field: 'shtNm',
      width: COLUMN_WIDTHS.SJG.SHT_NM,
    },
    { title: 'Sjg.Chr.txtLotNo', field: 'lotNo', width: COLUMN_WIDTHS.LOT_NO },
    {
      title: 'Sjg.Chr.txtVerifyCat',
      field: 'verifyCatNm',
      width: COLUMN_WIDTHS.SJG.VER_CAT_NM,
    },
    {
      title: 'Sjg.Chr.txtGMPMngNo',
      field: 'gmpMngNo',
      width: COLUMN_WIDTHS.SYS_SEQ_NO,
    },
    {
      title: 'Sjg.Chr.txtGMPTitle',
      field: 'gmpTitle',
      width: COLUMN_WIDTHS.SJG.GMP_TITLE,
    },
    {
      title: 'Sjg.Chr.txtGMPDes',
      field: 'gmpDes',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: '',
      field: 'recordChg',
      formatter: 'btn',
      hozAlign: 'center',
    },
    { title: '', field: 'gmpContentUniqueKey', hidden: true },
  ],
  tableData: [],
  noUseConditionSearch: true, // 絞り込み条件無し
  showConditionSearch: false, // ConditionSearch不要
});

export const getSjgConfirmInspectionFormItems: () => CustomFormType['formItems'] =
  () => ({
    mfgMgrQcRslt: {
      label: { text: t('Sjg.Chr.txtmanufactureQualityManageResult') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'mfgMgrQcRslt',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
  });

export const sjgConfirmInspectionFormModel: CustomFormType['formModel'] =
  createFormModelByFormItems(getSjgConfirmInspectionFormItems());

export const getSjgConfirmInspectionFormItems1: () => CustomFormType['formItems'] =
  () => ({
    deviateMgmtExistMeas: {
      label: { text: t('Sjg.Chr.txtDeviationHaveOrNotHandleContent') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'deviateMgmtExistMeas',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    changeMgmtExistMeas: {
      label: { text: t('Sjg.Chr.txtChangeManageHaveOrNot') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'changeMgmtExistMeas',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
  });

export const sjgConfirmInspectionFormModel1: CustomFormType['formModel'] =
  createFormModelByFormItems(getSjgConfirmInspectionFormItems1());

export const getSjgConfirmInspectionFormItems2: () => CustomFormType['formItems'] =
  () => ({
    releaseRsltM: {
      label: { text: t('Sjg.Chr.txtPlantShipmentDecisionResult') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'releaseRsltM',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
  });

export const sjgConfirmInspectionFormModel2: CustomFormType['formModel'] =
  createFormModelByFormItems(getSjgConfirmInspectionFormItems2());

export const getSjgConfirmInspectionFormItems3: () => CustomFormType['formItems'] =
  () => ({
    mfgPlantRelExecConf: {
      label: { text: t('Sjg.Chr.txtConfirmSjgCorrectlyImplemented') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'mfgPlantRelExecConf',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    mfgPlantRelRslt: {
      label: { text: t('Sjg.Chr.txtManufacturingShipmentDecisionResult') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'mfgPlantRelRslt',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    deviateOnMktRelProc: {
      label: { text: t('Sjg.Chr.txtDeviationMarketDeliveryProcedure') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'deviateOnMktRelProc',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    qaMgrInstOnDeviate: {
      label: { text: t('Sjg.Chr.txtInstructionsQualityDirectorDeviation') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'qaMgrInstOnDeviate',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    mtaProdQltEfficacySafe: {
      label: { text: t('Sjg.Chr.txtInformationMaterialsProductQuality') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'mtaProdQltEfficacySafe',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    productTestReport: {
      label: { text: t('Sjg.Chr.txtTestReport') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'productTestReport',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    whetherMaterialRevised: {
      label: { text: t('Sjg.Chr.txtWhetherMaterialRevised') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'whetherMaterialRevised',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
    releaseRsltQ: {
      label: { text: t('Sjg.Chr.txtMarketShipmentDecisionResult') },
      formModelValue: '',
      formRole: 'selectComboBox',
      selectOptions: [],
      cmbId: 'releaseRsltQ',
      rules: [rules.required('selectComboBox')],
      tags: [{ text: t('Cm.Chr.txtTagOfRequired'), type: 'danger' }],
      span: 8,
    },
  });

export const sjgConfirmInspectionFormModel3: CustomFormType['formModel'] =
  createFormModelByFormItems(getSjgConfirmInspectionFormItems3());

export const getSjgConfirmInspectionFormItems4: () => CustomFormType['formItems'] =
  () => ({
    releaseExpl: {
      label: { text: t('Sjg.Chr.txtGMPDes') },
      formModelValue: '',
      formRole: 'textComboBox',
      cmbId: 'sjgVerifyRslt',
      selectOptions: [],
      rules: [rules.length(64, t('Cm.Chr.txtLength', [64]))],
      span: 8,
    },
  });

export const sjgConfirmInspectionFormModel4: CustomFormType['formModel'] =
  createFormModelByFormItems(getSjgConfirmInspectionFormItems4());
