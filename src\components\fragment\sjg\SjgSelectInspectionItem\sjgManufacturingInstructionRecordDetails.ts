import { TabulatorTableIF } from '@/components/model/common/TabulatorTable/TabulatorTable';
import COLUMN_WIDTHS from '@/constants/tabulatorColumnWidth';

/**
 * 'DL_WGT' : 秤量記録書DL
 * 'DL_PRD' : 製造記録書DL
 */
export const BUTTON_ID = {
  DL_WGT: 'btnDlWgt',
  DL_PRD: 'btnDlPrd',
} as const;

export const tablePropsDataBomModList: TabulatorTableIF = {
  pageName: 'BomModList',
  pageSize: 20,
  height: '100',
  dataID: 'riMatNo',
  showRadio: false,
  column: [
    {
      title: 'Sjg.Chr.txtCmtTimes',
      field: 'cmtTimes',
      formatter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SJG.CMT_TIMES,
    },
    {
      title: 'Sjg.Chr.txtMatNoDetail',
      field: 'riMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Sjg.Chr.txtDisplayNameJp',
      field: 'dspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    {
      title: 'Sjg.Chr.txtUnitNmJp',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Sjg.Chr.txtBeforeRecordValue',
      field: 'bRecVal1',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtAfterRecordValue',
      field: 'aRecVal1',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtBRecVal3',
      field: 'bRecVal3',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtBRecVal4',
      field: 'bRecVal4',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtBRecVal5',
      field: 'bRecVal5',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtARecVal2',
      field: 'aRecVal2',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtARecVal3',
      field: 'aRecVal3',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtARecVal4',
      field: 'aRecVal4',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtARecVal5',
      field: 'aRecVal5',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtRecordModifyDate',
      field: 'cmtDts',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtModifyUser',
      field: 'modUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
};

export const tablePropsDataPrdModList: TabulatorTableIF = {
  pageName: 'PrdModList',
  pageSize: 20,
  height: '100',
  dataID: 'riMatNo',
  column: [
    {
      title: 'Sjg.Chr.txtCmtTimes',
      field: 'cmtTimes',
      formatter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SJG.CMT_TIMES,
    },
    {
      title: 'Sjg.Chr.txtMatNoDetail',
      field: 'riMatNo',
      width: COLUMN_WIDTHS.MAT_NO,
    },
    {
      title: 'Sjg.Chr.txtDspNmJp',
      field: 'dspNmJp',
      width: COLUMN_WIDTHS.MAT_NM,
    },
    {
      title: 'Sjg.Chr.txtBeforeRecordValue',
      field: 'bRecVal1',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtAfterRecordValue',
      field: 'aRecVal1',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtRecordModifyDate',
      field: 'cmtDts',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtModifyUser',
      field: 'modUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
};
export const tablePropsDataSopModList: TabulatorTableIF = {
  pageName: 'SopModList',
  pageSize: 20,
  height: '100',
  dataID: 'sopFlowNmJp',
  column: [
    {
      title: 'Sjg.Chr.txtCmtTimes',
      field: 'cmtTimes',
      formatter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SJG.CMT_TIMES,
    },
    {
      title: 'Sjg.Chr.txtSopFlowNm',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    {
      title: 'Sjg.Chr.txtSopNodeTimes',
      field: 'sopNodeTimes',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SOP_NODE_TIMES,
    },
    {
      title: 'Sjg.Chr.txtWorkInstructionDetail',
      field: 'cmtMain',
      width: COLUMN_WIDTHS.SJG.CMT_MAIN,
    },
    {
      title: 'Sjg.Chr.txtBeforeRecordValue1',
      field: 'bRecVal1',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtAfterRecordValue1',
      field: 'aRecVal1',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtBeforeRecordValue2',
      field: 'bRecVal2',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtAfterRecordValue2',
      field: 'aRecVal2',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtBeforeRecordValue3',
      field: 'bRecVal3',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtAfterRecordValue3',
      field: 'aRecVal3',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtBeforeRecordValue4',
      field: 'bRecVal4',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtAfterRecordValue4',
      field: 'aRecVal4',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtBeforeRecordValue5',
      field: 'bRecVal5',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtAfterRecordValue5',
      field: 'aRecVal5',
      hozAlign: 'right',
      sorter: 'number',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtRecordModifyDate',
      field: 'cmtDts',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtModifyUser',
      field: 'modUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtComment',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
  ],
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
  textWrapColumns: ['cmtMain'], // 作業指示内容
};

export const tablePropsDataDevModList: TabulatorTableIF = {
  pageName: 'DevLogList',
  pageSize: 20,
  height: '100',
  dataID: 'prcNo',
  column: [
    {
      title: 'Sjg.Chr.txtDeviationLevel',
      field: 'devCorrLv',
      formatter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.DEV_LV,
    },
    {
      title: 'Sjg.Chr.txtRxNmJp',
      field: 'rxNmJp',
      width: COLUMN_WIDTHS.RX_NM,
    },
    {
      title: 'Sjg.Chr.txtSopFlowNmDetail',
      field: 'sopFlowNmJp',
      width: COLUMN_WIDTHS.SOP_FLOW_NM,
    },
    {
      title: 'Sjg.Chr.txtWorkInstructionDetail',
      field: 'cmtMain',
      width: COLUMN_WIDTHS.SJG.CMT_MAIN,
    },

    {
      title: 'Sjg.Chr.txtInstValDetail',
      field: 'instVal',
      width: COLUMN_WIDTHS.INST_VAL,
    },
    {
      title: 'Sjg.Chr.txtUnitNmJp',
      field: 'unitNmJp',
      width: COLUMN_WIDTHS.UNIT_NM,
    },
    {
      title: 'Sjg.Chr.txtRecVal1',
      field: 'recVal1',
      width: COLUMN_WIDTHS.SJG.REC_VAL,
    },
    {
      title: 'Sjg.Chr.txtThValType',
      field: 'thValType',
      width: COLUMN_WIDTHS.SJG.VAL_TYPE,
    },

    {
      title: 'Sjg.Chr.txtThValLlmt',
      field: 'thValLlmt',
      formatter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SJG.VAL_LMT,
    },
    {
      title: 'Sjg.Chr.txtThValUlmt',
      field: 'thValUlmt',
      formatter: 'number',
      hozAlign: 'right',
      width: COLUMN_WIDTHS.SJG.VAL_LMT,
    },
    {
      title: 'Sjg.Chr.txtEdDtsDetail',
      field: 'edDts',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    {
      title: 'Sjg.Chr.txtModUsr',
      field: 'modUsr',
      width: COLUMN_WIDTHS.USR_NM,
    },
    {
      title: 'Sjg.Chr.txtDevExpl',
      field: 'modExpl',
      width: COLUMN_WIDTHS.CMT_TXT,
    },
    {
      title: 'Sjg.Chr.txtConfirmDate',
      field: 'cmtDts',
      formatter: 'date',
      hozAlign: 'center',
      width: COLUMN_WIDTHS.DATE_YYYYMMDDHHMMSS,
    },
    // 背景色指定隠しカラムとして運用
    {
      title: '',
      field: 'backgroundColor',
      hidden: true,
    },
  ],
  rowColor: {
    useColor: true,
    colorColumn: 'backgroundColor',
  },
  tableData: [],
  showConditionSearch: false, // ConditionSearchの表示/非表示
  hideSearchHeader: true, // サーチヘッダー
  hideColumnEditBtn: true, // カラム編集ボタン
  hideCsvExportBtn: true, // CSV出力ボタン
  textWrapColumns: ['cmtMain'], // 作業指示内容
};
